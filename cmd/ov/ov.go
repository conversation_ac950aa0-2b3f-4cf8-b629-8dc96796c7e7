package main

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"sync"
	"time"

	"me_two/pkg/decimal"
	"me_two/pkg/match_api"
	"me_two/pkg/utils"

	"github.com/google/btree"
	"github.com/lirm/aeron-go/cluster"
	"github.com/rs/zerolog/log"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/trace"

	//"pgregory.net/rand"

	"me_two/cmd/ov/extensions/extBond/dto"
	"runtime/debug"
)

var Dec100, _ = decimal.NewFromString("100")

type Routine struct {
	RoutineId           string
	MarketId            string
	StateName           string
	AllowSelfTrade      bool
	AllowTimeInForceGTC bool
	AllowTimeInForceGTD bool
	AllowTimeInForceIOC bool
	AllowTimeInForceDAY bool
	AllowOrdTypeMarket  bool
	AllowOrdTypeLimit   bool
	AllowCreate         bool
	AllowCancel         bool
	AllowAmend          bool
	AllowMatch          bool
	UseEqPrice          bool
}

type Asset struct {
	Id                       int64
	AssetId                  string // ex_contract.code
	AssetAmountDecimals      int64  // ex_contract.digits
	BalanceAccountCode       string // ex_asset_master_list.code
	InstrumentType           string
	IsTradeOnCleanPrice      bool
	BondDaycount             string
	CouponRate               decimal.Decimal
	CouponRateType           string
	DatedDate                string
	FaceValue                decimal.Decimal
	InterestCalculationTime  string
	IssueDate                string
	MaturityDate             string
	Timezone                 string
	DenominationCurrencyId   int64
	DenominationCurrencyCode string
	CouponDates              []*dto.CouponDate
	UpdateTime			   int64 
}

type Market struct {
	ID                         int64
	Name                       string
	Code                       string
	MarketId                   string // Code
	Timezone                   *time.Location
	ActiveRoutine              *Routine
	AllowSelfTrade             bool
	OperatorID                 int64
	OperatorCode               string
	EnableFees                 sql.NullBool
	EnableRiskControl          sql.NullBool
	SkipSellFeesEarmark        sql.NullBool
	EnableCustody              sql.NullBool
	PositionKeeping            sql.NullBool
	EnableMarketSurveillance   sql.NullBool
	ClobOrderTypes             string
	OrderTypes                 []string
	MarketTifs                 []string
	LimitTifs                  []string
	StopTifs                   []string
	mu                         sync.Mutex
	Assets                     map[string]*Asset // key is ex_contract.code
	EnableExtSettlementConfirm bool
	MarketModel                string
}

type Ticker struct {
	Symbol            string
	MarketCode        string
	FeeRate           decimal.Decimal
	BufferRate        decimal.Decimal
	QtyMin            decimal.Decimal
	QtyMax            decimal.Decimal
	QtyMult           decimal.Decimal
	QtyDecimals       int
	PriceDecimals     int
	PriceMult         decimal.Decimal
	InstrumentType    string
	TradeOnCleanPrice bool
	mu                sync.Mutex
}

type BalanceAccount struct {
	BalanceAccountId      string
	HoldingAccountId      string
	AssetSymbol           string
	AvailBalance          decimal.Decimal
	TotalBalance          decimal.Decimal
	EarmarkWithdraw       decimal.Decimal
	PendingIN             decimal.Decimal
	PendingOUT            decimal.Decimal
	AdjustEarmarkBalances map[dto.OrderId]*dto.AdjustEarmarkBalance
}

type HoldingAccount struct {
	HoldingAccountId string // td.account.nam
	// no use
	// ServiceAccountId string
	// no use
	// BalanceAccounts  map[string]*BalanceAccount
	Markets         map[string]*HoldingAccountMarket // key is market code. to find default SAA for the aha based on the market
	AccountType     string
	AccountTypeCode string
	State           string
	ExtOvName       string
	ID              int64
	Uuid            string
	Number          string
	CanDeposit      sql.NullBool
	CanTransfer     sql.NullBool
	CanWithdraw     sql.NullBool
	Status          string
	mu              sync.Mutex
	SaaUUID         string
	MktCode         string
}

type HoldingAccountMarket struct {
	HoldingAccountId      string
	MarketCode            string
	DefaultServiceAccount *ServiceAccount
	TaxRate               decimal.Decimal
	TaxCode               string
	TaxDbId               uint64
	mu                    sync.Mutex
	DefaultSaaUUID        string
}

type ServiceAccount struct {
	ServiceAccountId             string // ex.service.access.account.uuid
	ApiKey                       string
	AllowBuy                     bool
	AllowSell                    bool
	AllowTrade                   bool
	MakerFeeRate                 decimal.Decimal
	TakerFeeRate                 decimal.Decimal
	HoldingAccounts              map[string]*HoldingAccount // key is td.account.name
	ServiceAccessAccountTypeCode string
	MemberTypeCode               string
	ID                           int64
	Name                         string
	UUID                         string
	Status                       string
	CanBuy                       sql.NullBool
	CanSell                      sql.NullBool
	MemberID                     int64
	MemberName                   string
	MemberCode                   string
	MarketID                     int64
	MarketName                   string
	MarketCode                   string
	AhaIDs                       []int64
	AhaUUIDS                     []string
	mu                           sync.Mutex
}

type PriceLevel struct {
	Price  decimal.Decimal
	Qty    decimal.Decimal
	Offset uint64
}

type OrderBook struct {
	Symbol      string
	BidLevels   *btree.BTreeG[*PriceLevel]
	AskLevels   *btree.BTreeG[*PriceLevel]
	Mutex       sync.RWMutex
	open_orders map[string]*OpenOrder
}

type OpenOrder struct {
	Symbol           string
	OrderId          string
	ClOrdId          string
	OpenQty          decimal.Decimal
	Price            decimal.Decimal
	HoldingAccountId string
	ServiceAccountId string
	Side             match_api.Side
	SenderCompId     string
	MarketCode       string
	InterestAmt      decimal.Decimal
}

type TradeVenueAccount struct {
	AccountID        int64
	AhaNumber        string
	VenueMarketCode  string
	Code             string
	Name             string
	MemberCode       string
	MarketCode       string
	EntityType       string
	VenueAccountName string
	mu               sync.Mutex
}

type PsOrderExecReport struct {
	OrderId              string
	MarketCode           string
	Side                 match_api.Side
	Symbol               string
	ServiceAccountId     string
	LastFillQuantity     *match_api.UDec128
	LastFillPrice        *match_api.UDec128
	PsTradeId            string
	CumulativeFillAmount *match_api.UDec128
}

type PsEarmarkInfo struct {
	RemainingEarmark                decimal.Decimal
	RemainingEarmarkFee             decimal.Decimal
	RemainingEarmarkTax             decimal.Decimal
	OperatorEarmarkBalanceAccountId string
}

var psOrderPool = sync.Pool{
	New: func() interface{} {
		return &match_api.PsOrder{}
	},
}

func newPsOrder() *match_api.PsOrder {
	order := psOrderPool.Get().(*match_api.PsOrder)
	*order = match_api.PsOrder{} // clear data
	//return orderPool.Get().(*Order)
	return order
}

func freePsOrder(order *match_api.PsOrder) {
	psOrderPool.Put(order)
}

// move to cache.go
// var holding_accounts = make(map[string]*HoldingAccount) // key td.account.name
// var service_accounts = make(map[string]*ServiceAccount) // key ex.service.access.acounnt.uuid
var balance_accounts = make(map[string]*BalanceAccount) // key opr_code:aha_name:aml_code
var psEarmarkCache = make(map[string]PsEarmarkInfo)

var disable_balance_check = false
var disable_check_duplicate = false
var disable_send_match_out = false
var disable_send_match_responses = false
var disable_replay_match_out = false
var disable_replay_sor_out = false
var enable_print_metrics = false

// use for chunk response
var ovChunkResponsesList = make([]*match_api.Response, 0, 100)
var ovCurrentChunkTag = ""
var ovTotalResponseInChunk = 0

type MemberType struct {
	MemberTypeCode string
}

// 18-Nov-2024. change to use sequence instead!
// var ov_rand *rand.Rand
var use_cluster bool

const STATUS_APPROVED = "approved"
const BALANCE_REFERENCE_EARMARK = "Earmark "
const BALANCE_REFERENCE_TRADE = "Trade "
const BALANCE_REFERENCE_TRADE_FEE = "Trade Fee "
const BALANCE_REFERENCE_TRADE_TAX = "Trade Tax "
const BALANCE_REFERENCE_RELEASE_EARMARK = "Release Earmark "
const BALANCE_REFERENCE_REJECR_ORDER = "Reject Order "
const BALANCE_REFERENCE_CANCEL_ORDER = "Cancel Order "

const SUFFIX_DEALER = "-dealer"

const BASE_ACCOUNT_IS_NIL_ERRORR = "base account is nil"
const QUOTE_ACCOUNT_IS_NIL_ERRORR = "quote account is nil"

func get_balance_account(bal_acc_id string) *BalanceAccount {
	ba := balance_accounts[bal_acc_id]
	if ba == nil {
		ba = &BalanceAccount{BalanceAccountId: bal_acc_id}
		balance_accounts[bal_acc_id] = ba
	}
	return ba
}

// Retrieve the earmark value for a given orderId
// If the orderId is not found in the cache, return initial value
func getPsEarmarkCache(orderId string) (PsEarmarkInfo, bool) {
	psEarmarkInfo, exists := psEarmarkCache[orderId]
	if !exists {
		log.Warn().Msgf("Ps earmark cache not found: %s", orderId)
		return PsEarmarkInfo{}, false
	}
	return psEarmarkInfo, true
}

func deletePsEarmarkCache(orderId string) {
	_, exists := psEarmarkCache[orderId]
	if exists {
		delete(psEarmarkCache, orderId)
		log.Debug().Msgf("Deleted ps earmark cache for order %s", orderId)
	} else {
		log.Warn().Msgf("Earmark cache for order %s does not exist, no deletion needed", orderId)
	}
}

func new_rand_balance_update_id() string {
	//18-Nov-2024. change to use sequence instead!
	//n := ov_rand.Int63n(100_000_000_000_000)
	//return fmt.Sprintf("L-%014d", n)
	return NewBalanceID()
}

func update_balance(order_id string, market_code string, ba *BalanceAccount, amt decimal.Decimal, update_type match_api.BalanceUpdateType, ref string, check_avail bool, scenario_type match_api.ScenarioType, scenario match_api.Scenario,
	pk_enabled bool, saa_uuid, ticker_code string, side match_api.Side, trade_qty, trade_price, trade_fee, trade_tax *match_api.UDec128, maker_taker_type string) error {
	if log_balance {
		log.Info().Msgf("[BALANCE_BEFORE] update_balance order_id=%s balanceAccountId=%s  avail=%s, total=%s txnAmt=%s",
			order_id, ba.BalanceAccountId, ba.AvailBalance.String(), ba.TotalBalance.String(), amt.String())
	}
	new_avail_bal := ba.AvailBalance.Add(amt)
	if update_type == match_api.BalanceUpdateType_BALANCE_UPDATE_TOTAL_AVAIL || update_type == match_api.BalanceUpdateType_BALANCE_UPDATE_AVAIL_ONLY {
		ba.AvailBalance = new_avail_bal
	}
	if update_type == match_api.BalanceUpdateType_BALANCE_UPDATE_TOTAL_AVAIL || update_type == match_api.BalanceUpdateType_BALANCE_UPDATE_TOTAL_ONLY {
		ba.TotalBalance = ba.TotalBalance.Add(amt)
	}
	if log_balance {
		log.Info().Msgf("[BALANCE_AFTER] update_balance order_id=%s balanceAccountId=%s avail=%s, total=%s",
			order_id, ba.BalanceAccountId, ba.AvailBalance.String(), ba.TotalBalance.String())
	}
	bu := match_api.BalanceUpdate{}
	bu.BalanceAccountId = ba.BalanceAccountId
	bu.AssetSymbol = ba.AssetSymbol
	bu.OrderId = order_id
	bu.Ref = ref
	bu.UpdateType = update_type
	bu.UpdateAmount = amt.ToProtoAlloc()
	bu.TotalPendingOut = ba.PendingOUT.ToProtoAlloc()
	bu.TotalPendingIn = ba.PendingIN.ToProtoAlloc()
	bu.BalanceAvail = ba.AvailBalance.ToProtoAlloc()
	bu.BalanceTotal = ba.TotalBalance.ToProtoAlloc()
	bu.MarketCode = market_code
	bu.Scenario = scenario
	bu.ScenarioType = scenario_type
	bu.BalanceUpdateId = new_rand_balance_update_id()
	bu.Time = uint64(GetClusterTimeUnixNano())
	bu.NegUpdateAmount = false
	bu.PositionKeepingInfo = nil
	bu.MakerTakerType = maker_taker_type
	if pk_enabled {
		bu.PositionKeepingInfo = &match_api.PositionKeepingInfo{
			PkEnabled:        pk_enabled,
			ContractRef:      ticker_code,
			Side:             side,
			Qty:              trade_qty,
			Price:            trade_price,
			ServiceAccountId: saa_uuid,
			Fee:              trade_fee,
			Tax:              trade_tax,
		}
	}
	resp := &match_api.Response{}
	resp.Response = &match_api.Response_BalanceUpdate{BalanceUpdate: &bu}
	resp_list := match_api.ResponseList{}
	resp_list.Responses = append(resp_list.Responses, resp)
	aeron_ov_out_send(&resp_list, "ov_update_balance")
	return nil
}

func update_balance_neg(order_id string, market_code string, ba *BalanceAccount, amt decimal.Decimal, update_type match_api.BalanceUpdateType, ref string, check_avail bool, scenario_type match_api.ScenarioType, scenario match_api.Scenario,
	pk_enabled bool, saa_uuid, ticker_code string, side match_api.Side, trade_qty, trade_price, trade_fee, trade_tax *match_api.UDec128, maker_taker_type string) error {
	if log_balance {
		log.Info().Msgf("[BALANCE_BEFORE] update_balance_neg order_id=%s balanceAccountId=%s avail=%s, total=%s txnAmt=%s",
			order_id, ba.BalanceAccountId, ba.AvailBalance.String(), ba.TotalBalance.String(), amt.String())
	}
	if check_avail && !disable_balance_check && ba.AvailBalance.LessThan(amt) {
		return fmt.Errorf("Insufficient balance: %s < %s, balance_account=%s", ba.AvailBalance.String(), amt.String(), ba.BalanceAccountId)
	}
	if update_type == match_api.BalanceUpdateType_BALANCE_UPDATE_TOTAL_AVAIL || update_type == match_api.BalanceUpdateType_BALANCE_UPDATE_AVAIL_ONLY {
		ba.AvailBalance = ba.AvailBalance.Sub(amt)
	}
	if update_type == match_api.BalanceUpdateType_BALANCE_UPDATE_TOTAL_AVAIL || update_type == match_api.BalanceUpdateType_BALANCE_UPDATE_TOTAL_ONLY {
		ba.TotalBalance = ba.TotalBalance.Sub(amt)
	}
	if log_balance {
		log.Info().Msgf("[BALANCE_AFTER] update_balance_neg order_id=%s balanceAccountId=%s avail=%s, total=%s",
			order_id, ba.BalanceAccountId, ba.AvailBalance.String(), ba.TotalBalance.String())
	}
	bu := match_api.BalanceUpdate{}
	bu.BalanceAccountId = ba.BalanceAccountId
	bu.AssetSymbol = ba.AssetSymbol
	bu.OrderId = order_id
	bu.Ref = ref
	bu.UpdateType = update_type
	bu.TotalPendingOut = ba.PendingOUT.ToProtoAlloc()
	bu.TotalPendingIn = ba.PendingIN.ToProtoAlloc()
	bu.UpdateAmount = amt.ToProtoAlloc()
	bu.BalanceAvail = ba.AvailBalance.ToProtoAlloc()
	bu.BalanceTotal = ba.TotalBalance.ToProtoAlloc()
	bu.MarketCode = market_code
	bu.Scenario = scenario
	bu.ScenarioType = scenario_type
	bu.NegUpdateAmount = true
	bu.BalanceUpdateId = new_rand_balance_update_id()
	bu.Time = uint64(GetClusterTimeUnixNano())
	bu.NegUpdateAmount = true
	bu.PositionKeepingInfo = nil
	bu.MakerTakerType = maker_taker_type
	if pk_enabled {
		bu.PositionKeepingInfo = &match_api.PositionKeepingInfo{
			PkEnabled:        pk_enabled,
			ContractRef:      ticker_code,
			Side:             side,
			Qty:              trade_qty,
			Price:            trade_price,
			ServiceAccountId: saa_uuid,
			Fee:              trade_fee,
			Tax:              trade_tax,
		}
	}
	resp := &match_api.Response{}
	resp.Response = &match_api.Response_BalanceUpdate{BalanceUpdate: &bu}
	resp_list := match_api.ResponseList{}
	resp_list.Responses = append(resp_list.Responses, resp)
	aeron_ov_out_send(&resp_list, "update_balance_neg")
	return nil
}

func get_buy_fill(ob *OrderBook, qty decimal.Decimal) (fill_qty, fill_amount decimal.Decimal) {
	fill_qty = decimal.Zero
	fill_amount = decimal.Zero
	ob.AskLevels.Ascend(func(level *PriceLevel) bool {
		if level.Qty.LessThanOrEqual(qty) {
			fill_qty = fill_qty.Add(level.Qty)
			fill_amount = fill_amount.Add(level.Qty.Mul(level.Price))
			qty = qty.Sub(level.Qty)
		} else {
			fill_qty = fill_qty.Add(qty)
			fill_amount = fill_amount.Add(qty.Mul(level.Price))
			qty = decimal.Zero
		}
		if qty.IsZero() {
			return false
		}
		return true
	})
	return fill_qty, fill_amount
}

func get_sell_fill(ob *OrderBook, qty decimal.Decimal) (fill_qty, fill_amount decimal.Decimal) {
	fill_qty = decimal.Zero
	fill_amount = decimal.Zero
	ob.BidLevels.Descend(func(level *PriceLevel) bool {
		if level.Qty.LessThanOrEqual(qty) {
			fill_qty = fill_qty.Add(level.Qty)
			fill_amount = fill_amount.Add(level.Qty.Mul(level.Price))
			qty = qty.Sub(level.Qty)
		} else {
			fill_qty = fill_qty.Add(qty)
			fill_amount = fill_amount.Add(qty.Mul(level.Price))
			qty = decimal.Zero
		}
		if qty.IsZero() {
			return false
		}
		return true
	})
	return fill_qty, fill_amount
}

func check_place_order_balances(order *match_api.Order) error {
	ticker := tickers[order.Symbol]
	if ticker == nil {
		return fmt.Errorf("Ticker not found, symbol: %s", order.Symbol)
	}
	_, ticker_sym := utils.SplitMarket(order.Symbol)
	base_sym, quote_sym := utils.SplitSym(ticker_sym)
	var err, mkt, market_code = setPlaceOrderMarketCode(order, ticker)
	if err != nil {
		return err
	}
	operator_code := mkt.OperatorCode

	base_asset := mkt.Assets[base_sym]
	if base_asset == nil {
		return fmt.Errorf("Base asset not found: %s", base_sym)
	}
	quote_asset := mkt.Assets[quote_sym]
	if quote_asset == nil {
		return fmt.Errorf("Quote asset not found: %s", quote_sym)
	}

	err, ha, sa := setAhaAndSaa(order.HoldingAccountId, order.ServiceAccountId, market_code)
	if err != nil {
		return err
	}
	order.ServiceAccountId = sa.ServiceAccountId

	err, qty, price := validateOrderQtyAndPrice(order.Qty, order.Price, order.OrdType, ticker)
	if err != nil {
		return err
	}

	mkt_acc := ha.Markets[market_code]
	var fee_rate decimal.Decimal
	var tax_rate decimal.Decimal
	if mkt_acc != nil {
		tax_rate = mkt_acc.TaxRate
		order.TaxRate = tax_rate.ToProtoAlloc()
		order.TaxId = mkt_acc.TaxDbId
	}
	fee_rate = ticker.FeeRate
	order.FeeRate = fee_rate.ToProtoAlloc()

	order.BaseBalanceAccountId = operator_code + ":" + order.HoldingAccountId + ":" + base_asset.BalanceAccountCode
	order.QuoteBalanceAccountId = operator_code + ":" + order.HoldingAccountId + ":" + quote_asset.BalanceAccountCode
	//order.BaseBalanceAccountId = operator_code + ":" + order.HoldingAccountId + ":" + base_sym
	//order.QuoteBalanceAccountId = operator_code + ":" + order.HoldingAccountId + ":" + quote_sym
	base_acc := get_balance_account(order.BaseBalanceAccountId)
	quote_acc := get_balance_account(order.QuoteBalanceAccountId)
	var ba *BalanceAccount
	var earmarkAmount decimal.Decimal
	var est_vwap decimal.Decimal
	if order.Side == match_api.Side_SIDE_BUY {
		ba = quote_acc
		err, earmarkAmount, est_vwap = setEarmarkAndOrderAmountBuy(sa, qty, price, ticker, order)

		if err != nil {
			return err
		}
	} else {
		ba = base_acc
		err, earmarkAmount, est_vwap = setEarmarkAndOrderAmountSell(sa, quote_asset, qty, price, ticker, order)

		if err != nil {
			return err
		}
	}

	err, earmark_fee, earmark_tax := setOrderFeeAndTax(order, price, qty, earmarkAmount, ha, sa, mkt, market_code, operator_code)

	if err != nil {
		return err
	}

	var scenario match_api.Scenario
	var scenario_fees match_api.Scenario
	var scenario_tax match_api.Scenario
	if order.Side == match_api.Side_SIDE_BUY {
		scenario = match_api.Scenario_SCENARIO_NEW_ORDER_BUY
		scenario_fees = match_api.Scenario_SCENARIO_NEW_ORDER_BUY_FEES
		scenario_tax = match_api.Scenario_SCENARIO_NEW_ORDER_BUY_TAX
	} else {
		scenario = match_api.Scenario_SCENARIO_NEW_ORDER_SELL
		scenario_fees = match_api.Scenario_SCENARIO_NEW_ORDER_SELL_FEES
		scenario_tax = match_api.Scenario_SCENARIO_NEW_ORDER_SELL_TAX
	}
	err = update_balance_neg(order.OrderId, order.MarketCode, ba, earmarkAmount, match_api.BalanceUpdateType_BALANCE_UPDATE_AVAIL_ONLY, BALANCE_REFERENCE_EARMARK+order.OrderId, true, match_api.ScenarioType_SCENARIO_TYPE_BALANCE, scenario,
		false, "", "", order.Side, nil, nil, nil, nil, "")
	if err != nil {
		return err
	}
	if !earmark_fee.IsZero() {
		err = update_balance_neg(order.OrderId, order.MarketCode, quote_acc, earmark_fee, match_api.BalanceUpdateType_BALANCE_UPDATE_AVAIL_ONLY, "Earmark Fee "+order.OrderId, true, match_api.ScenarioType_SCENARIO_TYPE_FEES, scenario_fees,
			false, "", "", order.Side, nil, nil, nil, nil, "")
		if err != nil {
			return err
		}
	}
	if !earmark_tax.IsZero() {
		err = update_balance_neg(order.OrderId, order.MarketCode, quote_acc, earmark_tax, match_api.BalanceUpdateType_BALANCE_UPDATE_AVAIL_ONLY, "Earmark Tax "+order.OrderId, true, match_api.ScenarioType_SCENARIO_TYPE_TAX, scenario_tax,
			false, "", "", order.Side, nil, nil, nil, nil, "")
		if err != nil {
			return err
		}
	}

	err = bondService.ProcessBondPlaceNewOrder(order)
	if err != nil {
		// keep processing even if the ext has error
		log.Error().Msgf("Error processing bond place new order: %v", err)
	}

	order.EarmarkAmt = earmarkAmount.ToProtoAlloc()
	order.EarmarkFeeAmt = earmark_fee.ToProtoAlloc()
	order.EarmarkTaxAmt = earmark_tax.ToProtoAlloc()
	if order.OrdType == match_api.OrdType_ORD_TYPE_MARKET {
		order.Price = est_vwap.ToProtoAlloc()
	}
	return nil
}

func check_edit_order_balances(order *match_api.Order) error {
	if order.OrdType != match_api.OrdType_ORD_TYPE_LIMIT {
		return fmt.Errorf("Invalid order type")
	}

	ticker := tickers[order.Symbol]
	if ticker == nil {
		return fmt.Errorf("Ticker not found, symbol: %s", order.Symbol)
	}

	ob := orderbooks[order.Symbol]
	if ob == nil {
		return fmt.Errorf("Orderbook not found for symbol: %s", order.Symbol)
	}
	orig_order := ob.open_orders[order.OrigClOrdId]
	if orig_order == nil {
		return fmt.Errorf("Order not found: %s", order.ClOrdId)
	}
	orig_qty := orig_order.OpenQty
	orig_price := orig_order.Price

	_, ticker_sym := utils.SplitMarket(order.Symbol)
	base_sym, quote_sym := utils.SplitSym(ticker_sym)
	var market_code string
	if strings.Contains(order.Symbol, ":") {
		market_code = strings.Split(order.Symbol, ":")[0]
	} else {
		if order.MarketCode != "" {
			market_code = order.MarketCode
		} else {
			market_code = ticker.MarketCode
		}
		order.Symbol = market_code + ":" + order.Symbol
	}
	order.MarketCode = market_code
	mkt := markets[order.MarketCode]
	if mkt == nil {
		return fmt.Errorf("invalid market code: %s", order.MarketCode)
	}
	operator_code := mkt.OperatorCode

	ha := holding_accounts[order.HoldingAccountId]
	if ha == nil {
		return fmt.Errorf("Holding account %s not found", order.HoldingAccountId)
	}
	var sa *ServiceAccount
	if order.ServiceAccountId != "" {
		sa = service_accounts[order.ServiceAccountId]
		if sa == nil {
			return fmt.Errorf("Service account not found: %v", order.ServiceAccountId)
		}
		if sa.HoldingAccounts[order.HoldingAccountId] == nil {
			return fmt.Errorf("Holding account %s not found for service account %s", order.HoldingAccountId, order.ServiceAccountId)
		}
	} else {
		mkt_acc := ha.Markets[market_code]
		if mkt_acc == nil {
			return fmt.Errorf("Market not found for holding account %s and market code %s", order.HoldingAccountId, market_code)
		}
		sa = mkt_acc.DefaultServiceAccount
		if sa == nil {
			return fmt.Errorf("Default service account not found for holding account %s and market code %s", order.HoldingAccountId, market_code)
		}
	}
	order.ServiceAccountId = sa.ServiceAccountId

	mkt_acc := ha.Markets[market_code]
	var fee_rate decimal.Decimal
	var tax_rate decimal.Decimal
	if mkt_acc != nil {
		tax_rate = mkt_acc.TaxRate
		order.TaxRate = tax_rate.ToProtoAlloc()
		order.TaxId = mkt_acc.TaxDbId
	}
	fee_rate = ticker.FeeRate
	order.FeeRate = fee_rate.ToProtoAlloc()

	order.BaseBalanceAccountId = operator_code + ":" + order.HoldingAccountId + ":" + base_sym
	order.QuoteBalanceAccountId = operator_code + ":" + order.HoldingAccountId + ":" + quote_sym
	base_acc := get_balance_account(order.BaseBalanceAccountId)
	quote_acc := get_balance_account(order.QuoteBalanceAccountId)
	var ba *BalanceAccount
	var order_amt decimal.Decimal
	var orig_order_amt decimal.Decimal
	var earmarkAmount decimal.Decimal
	var qty decimal.Decimal
	var price decimal.Decimal
	var diff_earmark_amt decimal.Decimal
	var diff_earmark_fee decimal.Decimal
	var diff_earmark_tax decimal.Decimal
	if order.Qty != nil {
		qty = decimal.NewFromProto(*order.Qty)
	} else {
		qty = orig_qty
	}
	if order.Price != nil {
		price = decimal.NewFromProto(*order.Price)
	} else {
		price = orig_price
	}
	if order.Side == match_api.Side_SIDE_BUY {
		ba = quote_acc
		order_amt = qty.Mul(price)
		orig_order_amt = orig_qty.Mul(orig_price)
		diff_earmark_amt = order_amt.Sub(orig_order_amt)
	} else {
		ba = base_acc
		earmarkAmount = qty
		diff_earmark_amt = qty.Sub(orig_qty)
		order_amt = qty.Mul(price)
		orig_order_amt = orig_qty.Mul(orig_price)
	}
	var earmark_fee decimal.Decimal
	var earmark_tax decimal.Decimal

	if !fee_rate.IsZero() {
		earmark_fee = calc_fees_simple(order_amt, fee_rate)
		orig_earmark_fee := calc_fees_simple(orig_order_amt, fee_rate)
		diff_earmark_fee = earmark_fee.Sub(orig_earmark_fee)
		if !tax_rate.IsZero() {
			earmark_tax = calc_fees_simple(earmark_fee, tax_rate)
			orig_earmark_tax := calc_fees_simple(orig_earmark_fee, tax_rate)
			diff_earmark_tax = earmark_tax.Sub(orig_earmark_tax)
		}
	}

	var scenario match_api.Scenario
	var scenario_fees match_api.Scenario
	var scenario_tax match_api.Scenario
	if order.Side == match_api.Side_SIDE_BUY {
		scenario = match_api.Scenario_SCENARIO_NEW_ORDER_BUY
		scenario_fees = match_api.Scenario_SCENARIO_NEW_ORDER_BUY_FEES
		scenario_tax = match_api.Scenario_SCENARIO_NEW_ORDER_BUY_TAX
	} else {
		scenario = match_api.Scenario_SCENARIO_NEW_ORDER_SELL
		scenario_fees = match_api.Scenario_SCENARIO_NEW_ORDER_SELL_FEES
		scenario_tax = match_api.Scenario_SCENARIO_NEW_ORDER_SELL_TAX
	}
	if diff_earmark_amt.IsPositive() {
		err := update_balance_neg(order.OrderId, order.MarketCode, ba, diff_earmark_amt, match_api.BalanceUpdateType_BALANCE_UPDATE_AVAIL_ONLY, BALANCE_REFERENCE_EARMARK+order.OrderId, true, match_api.ScenarioType_SCENARIO_TYPE_BALANCE, scenario,
			false, "", "", order.Side, nil, nil, nil, nil, "")
		if err != nil {
			return err
		}
	} else if diff_earmark_amt.IsNegative() {
		err := update_balance(order.OrderId, order.MarketCode, ba, diff_earmark_amt, match_api.BalanceUpdateType_BALANCE_UPDATE_AVAIL_ONLY, BALANCE_REFERENCE_EARMARK+order.OrderId, true, match_api.ScenarioType_SCENARIO_TYPE_BALANCE, scenario,
			false, "", "", order.Side, nil, nil, nil, nil, "")
		if err != nil {
			return err
		}
	}
	if diff_earmark_fee.IsPositive() {
		err := update_balance_neg(order.OrderId, order.MarketCode, quote_acc, diff_earmark_fee, match_api.BalanceUpdateType_BALANCE_UPDATE_AVAIL_ONLY, "Earmark Fee "+order.OrderId, true, match_api.ScenarioType_SCENARIO_TYPE_FEES, scenario_fees,
			false, "", "", order.Side, nil, nil, nil, nil, "")
		if err != nil {
			return err
		}
	} else if diff_earmark_fee.IsNegative() {
		err := update_balance(order.OrderId, order.MarketCode, quote_acc, diff_earmark_fee, match_api.BalanceUpdateType_BALANCE_UPDATE_AVAIL_ONLY, "Earmark Fee "+order.OrderId, true, match_api.ScenarioType_SCENARIO_TYPE_FEES, scenario_fees,
			false, "", "", order.Side, nil, nil, nil, nil, "")
		if err != nil {
			return err
		}
	}
	if diff_earmark_tax.IsPositive() {
		err := update_balance_neg(order.OrderId, order.MarketCode, quote_acc, earmark_tax, match_api.BalanceUpdateType_BALANCE_UPDATE_AVAIL_ONLY, "Earmark Tax "+order.OrderId, true, match_api.ScenarioType_SCENARIO_TYPE_TAX, scenario_tax,
			false, "", "", order.Side, nil, nil, nil, nil, "")
		if err != nil {
			return err
		}
	} else if diff_earmark_tax.IsNegative() {
		err := update_balance(order.OrderId, order.MarketCode, quote_acc, earmark_tax, match_api.BalanceUpdateType_BALANCE_UPDATE_AVAIL_ONLY, "Earmark Tax "+order.OrderId, true, match_api.ScenarioType_SCENARIO_TYPE_TAX, scenario_tax,
			false, "", "", order.Side, nil, nil, nil, nil, "")
		if err != nil {
			return err
		}
	}

	order.EarmarkAmt = earmarkAmount.ToProtoAlloc()
	order.EarmarkFeeAmt = earmark_fee.ToProtoAlloc()
	order.EarmarkTaxAmt = earmark_tax.ToProtoAlloc()
	return nil
}

func process_request(req *match_api.Request, session cluster.ClientSession, cluster_ cluster.Cluster) {
	log.Debug().Msgf("process_request: %v", req)
	var span trace.Span
	if tracer != nil {
		if req.TraceId != "" {
			traceID, _ := trace.TraceIDFromHex(req.TraceId)
			sc := trace.NewSpanContext(trace.SpanContextConfig{
				TraceID:    traceID,
				SpanID:     trace.SpanID{}, // Generate new or use valid span ID
				TraceFlags: 0x1,            // Set the sampled flag
				Remote:     false,
			})
			_, span := tracer.Start(trace.ContextWithSpanContext(context.Background(), sc), "process_request")
			defer span.End()
		}
	}

	defer func() {
		if r := recover(); r != nil {
			log.Error().Stack().Msgf("Panic recovered in process_request: %v", r)

			// Print full stack trace
			stackTrace := string(debug.Stack())
			log.Error().Msgf("Stack trace of the Panic :\n%s", stackTrace)
		}
	}()

	var err error

	switch m := req.Request.(type) {
	case *match_api.Request_PlaceOrder:
		if span != nil {
			span.SetAttributes(attribute.String("cl_ord_id", m.PlaceOrder.ClOrdId))
		}
		err = validate_place_order(m.PlaceOrder, req, cluster_, session)
		metrics.PlaceOrders++
	case *match_api.Request_CancelOrder:
		if span != nil {
			span.SetAttributes(attribute.String("cl_ord_id", m.CancelOrder.ClOrdId))
			span.SetAttributes(attribute.String("orig_cl_ord_id", m.CancelOrder.OrigClOrdId))
		}
		validate_cancel_order(m.CancelOrder, req, cluster_, session)
		metrics.CancelOrders++
	case *match_api.Request_BulkCancelOrder:
		validateBulkCancelOrder(m.BulkCancelOrder, req, cluster_, session)
	case *match_api.Request_EditOrder:
		validate_edit_order(m.EditOrder, req, cluster_, session)
		metrics.EditOrders++
	case *match_api.Request_Deposit:
		process_deposit(m.Deposit, session)
		metrics.Deposits++
	case *match_api.Request_Withdraw:
		process_withdraw(m.Withdraw, session)
		metrics.Withdrawals++
	case *match_api.Request_WithdrawEarmark:
		process_withdraw_earmark(m.WithdrawEarmark, session)
		metrics.Withdrawals++
	case *match_api.Request_ClearBalance:
		process_clear_balance(m.ClearBalance)
	case *match_api.Request_Disconnect:
		process_disconnect(m.Disconnect)
	case *match_api.Request_Config:
		process_config_update(m.Config)
	case *match_api.Request_MatchResponses:
		process_response_list(m.MatchResponses)
	case *match_api.Request_TradesConfirm:
		handle_trades_confirm(m.TradesConfirm, session)
		/*for _, resp := range m.MatchResponses.Responses {
		      process_match_response(resp)
		  }
		  if !disable_send_match_responses {
		      aeron_ov_out_send(m.MatchResponses)
		  }*/
	case *match_api.Request_PsPlaceOrder:
		if span != nil {
			span.SetAttributes(attribute.String("cl_ord_id", m.PsPlaceOrder.ClOrdId))
		}
		psValidatePlaceOrder(req, cluster_, session)
		metrics.PsPlaceOrders++
		metrics.PsTotalOrders++

	case *match_api.Request_PsCancelOrder:
		if span != nil {
			span.SetAttributes(attribute.String("cl_ord_id", m.PsCancelOrder.ClOrdId))
		}
		psValidateCancelOrder(m.PsCancelOrder, req, cluster_, session)
		metrics.PsCancelOrders++

	case *match_api.Request_Transfer:
		processTransfer(m.Transfer, session)

	case *match_api.Request_BalanceSync:
		processBalanceSync(m.BalanceSync, session)

	case *match_api.Request_SettleTransfer:
		processSettleTransfer(m.SettleTransfer, session)
	case *match_api.Request_ApiEarmark:
		processApiEarmark(m.ApiEarmark, session)
	case *match_api.Request_OvLeaderReject:
		processOVLeaderReject(m.OvLeaderReject)
	case *match_api.Request_OvLeaderRejectPs:
		processOVLeaderRejectPS(m.OvLeaderRejectPs)
	}

	if err != nil {
		log.Error().Err(err).Msg("Failed to process request")
	} else {
		err = bondService.ProcessRequest(req, session)
		if err != nil {
			bondService.Log.Error().Err(err).Msg("Failed to process request")
		}
	}

	metrics.RequestsProcessed++
	metrics.RequestsValidated++ // XXX
}

// ER Proto Message is come from match-out and match-backup which is only leader will subscribe.
// inside ER proto has match_pos and match_backup_pos depend on the message come from which subscriber.
//   - when it come from match_out sub then match_pos has value and match_backup_pos is zero
//   - and when it match_backup sub then  match_backup_pos has value and match_pos is zero
//
// If node is not leader keep track match out seq here
// because when is leader match out seq is tracked on the subscriber it self!
// refer to aeron_sub_match_out.go and aeron_sub_match_out_backup.go (subscriber out seq)
func process_response_list(resp_list *match_api.ResponseList) {
	currSeq, ok := checkOutProcessedSeq(resp_list)
	if !ok {
		log.Debug().Msgf("[PROCESS_RESP_LIST] SKIP! seqCheckFailed. match_pos=%d match_backup_pos=%d sor_pos=%d sor_backup_pos=%d currSeq=%d respList=%v",
			resp_list.MatchOutPos, resp_list.MatchOutBackupPos,
			resp_list.SorOutPos, resp_list.SorOutBackupPos,
			currSeq, resp_list)
		return
	}

	for _, resp := range resp_list.Responses {
		process_match_response(resp)
	}
	if resp_list.MatchOutPos > 0 {
		match_out_last_pos = resp_list.MatchOutPos
	}
	if resp_list.MatchOutBackupPos > 0 {
		match_out_backup_last_pos = resp_list.MatchOutBackupPos
	}
	if resp_list.SorOutPos > 0 {
		sor_out_last_pos = resp_list.SorOutPos
	}
	if resp_list.SorOutBackupPos > 0 {
		sor_out_backup_last_pos = resp_list.SorOutBackupPos
	}

	updateProcessedOutSequence(resp_list)
	updateNonLeaderSubcriberOutSeq(resp_list)
	log.Debug().Msgf("[PROCESS_RESP_LIST] DONE! service=%s seq=%d match_pos=%d match_backup_pos=%d sor_pos=%d sor_backup_pos=%d",
		resp_list.FromService, resp_list.OutSequence,
		resp_list.MatchOutPos, resp_list.MatchOutBackupPos,
		resp_list.SorOutPos, resp_list.SorOutBackupPos)
}

func updateProcessedOutSequence(respList *match_api.ResponseList) {
	if respList.MatchOutPos > 0 || respList.MatchOutBackupPos > 0 {
		matchOutProcessedSEQMap[respList.FromService] = respList.OutSequence
	}
	if respList.SorOutPos > 0 || respList.SorOutBackupPos > 0 {
		sorOutProcessedSEQMap[respList.FromService] = respList.OutSequence
	}
}

func updateNonLeaderSubcriberOutSeq(respList *match_api.ResponseList) {
	if svc_ov_role != cluster.Leader {
		if respList.MatchOutPos > 0 || respList.MatchOutBackupPos > 0 {
			updateNonLeaderMatchOutSeq(respList)
		}
		if respList.SorOutPos > 0 || respList.SorOutBackupPos > 0 {
			updateNonLeaderSorOutSeq(respList)
		}
	}
}

func updateNonLeaderMatchOutSeq(respList *match_api.ResponseList) {
	aeron_sub_match_out_mutex.Lock()
	defer aeron_sub_match_out_mutex.Unlock()
	currSeq, _ := match_out_sequences[respList.FromService]
	if currSeq < respList.OutSequence {
		match_out_sequences[respList.FromService] = respList.OutSequence
		log.Debug().Msgf("[PROCESS_MATCH_RESP_LIST] host=%s non leader updateNonLeaderMatchOutSeq service=%s from seq=%d to seq=%d",
			host, respList.FromService, currSeq, respList.OutSequence)
	}
}

func updateNonLeaderSorOutSeq(respList *match_api.ResponseList) {
	aeron_sub_sor_out_mutex.Lock()
	defer aeron_sub_sor_out_mutex.Unlock()
	currSeq, _ := sor_out_sequences[respList.FromService]
	if currSeq < respList.OutSequence {
		sor_out_sequences[respList.FromService] = respList.OutSequence
		log.Debug().Msgf("[PROCESS_SOR_RESP_LIST] host=%s non leader updateNonLeaderSorOutSeq service=%s from seq=%d to seq=%d",
			host, respList.FromService, currSeq, respList.OutSequence)
	}
}

func checkOutProcessedSeq(respList *match_api.ResponseList) (uint64, bool) {
	if respList.MatchOutPos > 0 || respList.MatchOutBackupPos > 0 {
		return checkMatchOutProcessedSeq(respList)
	}
	if respList.MatchOutPos > 0 || respList.MatchOutBackupPos > 0 {
		return checkSorOutProcessedSeq(respList)
	}
	return 0, true
}

func checkMatchOutProcessedSeq(respList *match_api.ResponseList) (uint64, bool) {
	curr, ok := matchOutProcessedSEQMap[respList.FromService]
	if !ok {
		return 0, true
	}
	if curr >= respList.OutSequence {
		return curr, false
	}
	return curr, true
}

func checkSorOutProcessedSeq(respList *match_api.ResponseList) (uint64, bool) {
	curr, ok := sorOutProcessedSEQMap[respList.FromService]
	if !ok {
		return 0, true
	}
	if curr >= respList.OutSequence {
		return curr, false
	}
	return curr, true
}

func new_rand_order_id() string {
	//18-Nov-2024. change to use sequence instead!
	//n := ov_rand.Int63n(100_000_000_000_000)
	//return fmt.Sprintf("O-%014d", n)
	return NewOrderNUMBER()
}

func new_rand_exec_id() string {
	//18-Nov-2024. change to use sequence instead!
	//n := ov_rand.Int63n(100_000_000_000_000)
	//return fmt.Sprintf("E-%014d", n)
	return NewExecID()
}

func send_reject(order *match_api.Order, reason string, session cluster.ClientSession) {
	log.Debug().Msgf("send_reject: %v %v", order.OrderId, reason)
	resp := &match_api.Response{
		Response: &match_api.Response_ExecReport{
			ExecReport: &match_api.ExecReport{},
		},
	}
	exec := resp.Response.(*match_api.Response_ExecReport).ExecReport
	exec.ExecId = new_rand_exec_id()
	exec.ExecType = match_api.ExecType_EXEC_TYPE_REJECTED
	exec.Symbol = order.Symbol
	exec.OrderId = order.OrderId
	exec.ClOrdId = order.ClOrdId
	exec.Qty = order.Qty
	exec.Price = order.Price
	exec.Side = order.Side
	exec.OrdStatus = match_api.OrdStatus_ORD_STATUS_REJECTED
	exec.SenderCompId = order.SenderCompId
	exec.HoldingAccountId = order.HoldingAccountId
	exec.ServiceAccountId = order.ServiceAccountId
	exec.UserId = order.UserId
	exec.TransactTime = uint64(GetClusterTimeUnixNano())
	exec.Text = reason
	exec.OrdType = order.OrdType
	exec.TimeInForce = order.TimeInForce

	resp_list := match_api.ResponseList{}
	resp_list.SenderCompId = order.SenderCompId
	resp_list.RequestSentTime = order.RequestSentTime
	resp_list.RequestReceivedTime = order.RequestReceivedTime
	resp_list.RequestId = order.RequestId
	resp_list.Responses = append(resp_list.Responses, resp)
	if session != nil {
		aeron_ov_svc_send(session, &resp_list)
	}
	aeron_ov_out_send(&resp_list, "Clob_send_reject")
}

func validate_place_order(req *match_api.PlaceOrderReq, req_ *match_api.Request, cluster_ cluster.Cluster, session cluster.ClientSession) error {
	order := &match_api.Order{}
	order.OrderId = new_rand_order_id()
	order.RequestType = match_api.RequestType_REQUEST_TYPE_NEW_ORDER
	order.ClOrdId = req.ClOrdId
	order.Symbol = req.Symbol
	order.MarketCode = req.MarketCode
	order.Side = req.Side
	order.OrdType = req.OrdType
	order.Price = req.Price
	order.Qty = req.Qty
	order.TimeInForce = req.TimeInForce
	order.HoldingAccountId = req.HoldingAccountId
	order.ServiceAccountId = req.ServiceAccountId
	order.SenderCompId = req_.SenderCompId
	order.TraceId = req_.TraceId
	order.RequestReceivedTime = req_.RequestReceivedTime
	order.RequestId = req_.RequestId
	order.ExpireTime = 0
	if req.TimeInForce == match_api.TimeInForce_TIME_IN_FORCE_DAY || req.TimeInForce == match_api.TimeInForce_TIME_IN_FORCE_GOOD_TILL_DATE {
		order.ExpireTime = req.ExpireTime
	}
	order.UserId = req_.UserId
	order.BondPricePct = req.BondPricePct
	order.BondNominalAmount = req.BondNominalAmount

	err := check_place_order_balances(order)
	if err != nil {
		log.Error().Msgf("check_place_order_balances: %v", err)
		send_reject(order, err.Error(), session)
		return err
	}
	mr := match_api.Request{}
	mr.TraceId = req_.TraceId
	mr.Request = &match_api.Request_Order{Order: order}
	mr.GatewayName = req_.GatewayName
	if use_cluster {
		if cluster_.Role() == cluster.Leader {
			err := aeron_client_match_send(&mr)
			if err != nil {
				// call process_exec_report_reject to reverse the earmarked balance
				// CAN'T DO THIS. WILL CAUSING Machine State is Different between Leader and Follower
				//process_exec_report_reject(&match_api.ExecReport{
				//	SenderCompId:          order.SenderCompId,
				//	Side:                  order.Side,
				//	BaseBalanceAccountId:  order.BaseBalanceAccountId,
				//	QuoteBalanceAccountId: order.QuoteBalanceAccountId,
				//	OrderId:               order.OrderId,
				//	MarketCode:            order.MarketCode,
				//	EarmarkAmt:            order.EarmarkAmt,
				//	EarmarkFeeAmt:         order.EarmarkFeeAmt,
				//	EarmarkTaxAmt:         order.EarmarkTaxAmt,
				//	Qty:                   order.Qty,
				//})
				//send_reject(&order, "Downstream engine unavailable", session)
				sendOvLeaderReject(order, DownstreamEngineUnavailable, session, RequiredReverseEarmark)

				return err
			}
		}
	} else {
		aeron_match_in_send(&mr)
	}

	return nil
}

func psValidatePlaceOrder(req *match_api.Request, cluster_ cluster.Cluster, session cluster.ClientSession) {
	if !price_streaming_enabled {
		log.Error().Msg("PRICE_STREAMING IS NOT ENABLED. SHOULD NOT PROCEED!")
		return
	}
	err := psSendOrder(req, session, cluster_)(req.TraceId, req.GatewayName)
	if err != nil {
		log.Error().Msgf("PlaceOrderHandler: %v", err)
		return
	}
}

func psValidateCancelOrder(req *match_api.PsCancelOrderReq, req_ *match_api.Request, cluster_ cluster.Cluster, session cluster.ClientSession) {
	if !price_streaming_enabled {
		log.Error().Msg("PRICE_STREAMING IS NOT ENABLED. SHOULD NOT PROCEED!")
		return
	}
	order := match_api.PsOrder{}
	order.OrderId = req.OrderId
	order.RequestType = match_api.RequestType_REQUEST_TYPE_CANCEL_ORDER
	order.ClOrdId = req.ClOrdId
	order.OrigClOrdId = req.OrigClOrdId
	order.Symbol = req.Symbol
	order.MarketCode = req.MarketCode
	order.Side = req.Side
	order.HoldingAccountId = req.HoldingAccountId
	order.ServiceAccountId = req.ServiceAccountId
	order.SenderCompId = req_.SenderCompId
	order.TraceId = req_.TraceId
	order.RequestReceivedTime = req_.RequestReceivedTime
	order.RequestId = req_.RequestId
	order.UserId = req_.UserId
	order.Price = req.Price
	order.Qty = req.Quantity
	order.CancelReason = match_api.CancelReason_CANCEL_BY_USER
	mr := match_api.Request{}
	mr.Request = &match_api.Request_PsOrder{PsOrder: &order}
	mr.GatewayName = req_.GatewayName
	if use_cluster {
		if cluster_.Role() == cluster.Leader {
			err := aeron_client_sor_send(&mr)
			if err != nil {
				// can't do this causing replica leeader vs follower out of sync
				// psSendReject(&order, "Cancel Order Failed, Downstream engine unavailable", session)
				sendOvLeaderRejectPS(&order, CancelOrderFailedDownstreamEngineUnavailable, session, NotRequiredReverseEarmark)
			}
		}
	} else {
		aeron_client_sor_send(&mr)
	}
}

func validate_cancel_order(req *match_api.CancelOrderReq, req_ *match_api.Request, cluster_ cluster.Cluster, session cluster.ClientSession) {
	order := match_api.Order{}
	order.OrderId = new_rand_order_id()
	order.RequestType = match_api.RequestType_REQUEST_TYPE_CANCEL_ORDER
	order.ClOrdId = req.ClOrdId
	order.OrigClOrdId = req.OrigClOrdId
	order.Symbol = req.Symbol
	order.MarketCode = req.MarketCode
	order.Side = req.Side
	order.HoldingAccountId = req.HoldingAccountId
	order.ServiceAccountId = req.ServiceAccountId
	order.SenderCompId = req_.SenderCompId
	order.TraceId = req_.TraceId
	order.RequestReceivedTime = req_.RequestReceivedTime
	order.RequestId = req_.RequestId
	order.UserId = req_.UserId
	order.CancelReason = match_api.CancelReason_CANCEL_BY_USER
	mr := match_api.Request{}
	mr.Request = &match_api.Request_Order{Order: &order}
	mr.GatewayName = req_.GatewayName
	if use_cluster {
		if cluster_.Role() == cluster.Leader {
			err := aeron_client_match_send(&mr)
			if err != nil {
				// can't do this. will cause replica machine state out of sync
				//send_reject(&order, "Downstream engine unavailable", session)
				sendOvLeaderReject(&order, DownstreamEngineUnavailable, nil, NotRequiredReverseEarmark)
			}
		}
	} else {
		aeron_match_in_send(&mr)
	}
}

func validate_edit_order(req *match_api.EditOrderReq, req_ *match_api.Request, cluster_ cluster.Cluster, session cluster.ClientSession) {
	order := match_api.Order{}
	order.OrderId = new_rand_order_id()
	order.RequestType = match_api.RequestType_REQUEST_TYPE_EDIT_ORDER
	order.ClOrdId = req.ClOrdId
	order.OrigClOrdId = req.OrigClOrdId
	order.Symbol = req.Symbol
	order.Side = req.Side
	order.Price = req.Price
	order.Qty = req.Qty
	order.HoldingAccountId = req.HoldingAccountId
	order.ServiceAccountId = req.ServiceAccountId
	order.SenderCompId = req_.SenderCompId
	order.TraceId = req_.TraceId
	order.RequestReceivedTime = req_.RequestReceivedTime
	order.RequestId = req_.RequestId
	order.UserId = req_.UserId
	order.CancelReason = match_api.CancelReason_CANCEL_BY_USER
	order.OrdType = req.OrdType
	err := check_edit_order_balances(&order)
	if err != nil {
		log.Error().Msgf("check_edit_order_balances: %v", err)
		send_reject(&order, err.Error(), session)
		return
	}
	mr := match_api.Request{}
	mr.Request = &match_api.Request_Order{Order: &order}
	mr.GatewayName = req_.GatewayName
	if use_cluster {
		if cluster_.Role() == cluster.Leader {
			err := aeron_client_match_send(&mr)
			if err != nil {
				// can't do this. will cause replica machine state out of sync
				// send_reject(&order, "Downstream engine unavailable", session)
				sendOvLeaderReject(&order, DownstreamEngineUnavailable, session, RequiredReverseEarmark)
			}
		}
	} else {
		aeron_match_in_send(&mr)
	}
}

func process_deposit(req *match_api.DepositReq, session cluster.ClientSession) {
	log.Debug().Msgf("process_deposit: %v", req)

	if _, ok := holding_accounts[req.AccountId]; !ok {
		log.Debug().Msgf("reject deposit %s. reason account %s not found", req.DepositId, req.AccountId)

		bu := match_api.BalanceUpdate{}
		bu.BalanceAccountId = req.BalanceAccountId
		bu.AssetSymbol = req.ContractId
		bu.DepositId = req.DepositId
		bu.Ref = "Deposit " + req.DepositId
		bu.UpdateType = match_api.BalanceUpdateType_REJECT_BALANCE_UPDATE
		bu.ErrorCode = "13400"
		bu.Remark = "Asset account cannot be found"

		dp_resp := &match_api.DepositResp{
			DepositId:  req.DepositId,
			Status:     match_api.DepositStatus_DEPOSIT_STATUS_ERROR,
			ErrorCode:  "13400",
			Remark:     "Asset account cannot be found",
			ContractId: req.ContractId,
		}
		bu.DepositResp = dp_resp

		resp := &match_api.Response{}
		resp.Response = &match_api.Response_BalanceUpdate{BalanceUpdate: &bu}
		resp_list := match_api.ResponseList{}
		resp_list.Responses = append(resp_list.Responses, resp)
		aeron_ov_out_send(&resp_list, "reject_deposit")

		if session != nil {
			session_resp := match_api.Response{
				Response: &match_api.Response_Deposit{Deposit: dp_resp},
			}
			resp_list := match_api.ResponseList{}
			resp_list.Responses = append(resp_list.Responses, &session_resp)
			aeron_ov_svc_send(session, &resp_list)
		}
		return
	}

	// accept deposit
	ba := get_balance_account(req.BalanceAccountId)
	amt := decimal.NewFromProto(*req.Amount)
	if log_balance {
		log.Info().Msgf("[BALANCE_BEFORE] process_deposit deposit_id=%s avail=%s, total=%s txnAmt=%s",
			req.DepositId, ba.AvailBalance.String(), ba.TotalBalance.String(), amt.String())
	}
	log.Debug().Msgf("deposit amount: %v", amt)
	ba.AvailBalance = ba.AvailBalance.Add(amt)
	ba.TotalBalance = ba.TotalBalance.Add(amt)

	bu := match_api.BalanceUpdate{}
	bu.BalanceAccountId = ba.BalanceAccountId
	bu.AssetSymbol = ba.AssetSymbol
	bu.DepositId = req.DepositId
	bu.Ref = "Deposit " + req.DepositId
	bu.UpdateType = match_api.BalanceUpdateType_BALANCE_UPDATE_TOTAL_AVAIL
	bu.UpdateAmount = amt.ToProtoAlloc()
	bu.BalanceAvail = ba.AvailBalance.ToProtoAlloc()
	bu.BalanceTotal = ba.TotalBalance.ToProtoAlloc()
	//bu.MarketCode = market_code
	bu.Scenario = match_api.Scenario_SCENARIO_DEPOSIT
	bu.ScenarioType = match_api.ScenarioType_SCENARIO_TYPE_BALANCE
	bu.BalanceUpdateId = new_rand_balance_update_id()
	bu.Time = uint64(GetClusterTimeUnixNano())
	bu.DepositWithdrawalCost = req.Cost
	bu.PositionKeepingInfo = nil
	dp_resp := &match_api.DepositResp{
		DepositId:  req.DepositId,
		Status:     match_api.DepositStatus_DEPOSIT_STATUS_DONE,
		ContractId: req.ContractId,
	}
	bu.DepositResp = dp_resp
	//pk
	if req.PkEnabled {
		bu.PositionKeepingInfo = &match_api.PositionKeepingInfo{
			PkEnabled:        req.PkEnabled,
			ContractRef:      req.ContractId,
			Qty:              req.Amount,
			Price:            req.Cost,
			ServiceAccountId: req.ServiceAccountId,
		}
	}
	resp := &match_api.Response{}
	resp.Response = &match_api.Response_BalanceUpdate{BalanceUpdate: &bu}
	resp_list := match_api.ResponseList{}
	resp_list.Responses = append(resp_list.Responses, resp)
	aeron_ov_out_send(&resp_list, "accepted_deposit")
	if session != nil {

		session_resp := match_api.Response{
			Response: &match_api.Response_Deposit{Deposit: dp_resp},
		}
		resp_list := match_api.ResponseList{}
		resp_list.Responses = append(resp_list.Responses, &session_resp)
		aeron_ov_svc_send(session, &resp_list)
	}
	if log_balance {
		log.Info().Msgf("[BALANCE_AFTER] process_deposit deposit_id=%s avail=%s, total=%s",
			req.DepositId, ba.AvailBalance.String(), ba.TotalBalance.String())
	}
}

func process_withdraw(req *match_api.WithdrawReq, session cluster.ClientSession) {
	log.Debug().Msgf("process_withdraw %v", req)
	if _, ok := holding_accounts[req.AccountId]; !ok {
		log.Debug().Msgf("reject withdraw %s. reason account %s not found", req.WithdrawId, req.AccountId)

		wd_resp := match_api.WithdrawResp{
			WithdrawId: req.WithdrawId,
			Status:     match_api.WithdrawStatus_WITHDRAW_STATUS_ERROR,
			ErrorCode:  "13400",
			Remark:     "Asset account cannot be found",
			DbId:       req.DbId,
		}

		bu := match_api.BalanceUpdate{}
		bu.BalanceAccountId = req.BalanceAccountId
		bu.AssetSymbol = req.ContractId
		bu.WithdrawalId = req.WithdrawId
		bu.Ref = "Withdraw " + req.WithdrawId
		bu.UpdateType = match_api.BalanceUpdateType_REJECT_BALANCE_UPDATE
		bu.ErrorCode = "13400"
		bu.Remark = "Asset account cannot be found"
		bu.Withdraw = &wd_resp
		resp := &match_api.Response{}
		resp.Response = &match_api.Response_BalanceUpdate{BalanceUpdate: &bu}
		resp_list := match_api.ResponseList{}
		resp_list.Responses = append(resp_list.Responses, resp)
		aeron_ov_out_send(&resp_list, "reject_withdraw")

		if session != nil {

			session_resp := match_api.Response{
				Response: &match_api.Response_Withdraw{Withdraw: &wd_resp},
			}
			resp_list := match_api.ResponseList{}
			resp_list.Responses = append(resp_list.Responses, &session_resp)
			aeron_ov_svc_send(session, &resp_list)
		}
		return
	}
	ba := get_balance_account(req.BalanceAccountId)
	amt := decimal.NewFromProto(*req.Amount)
	if log_balance {
		log.Info().Msgf("[BALANCE_BEFORE] process_withdraw withdraw_id=%s balanceAccountId=%s avail=%s, total=%s earmarkWithdraw=%s txnAmt=%s",
			req.WithdrawId, ba.BalanceAccountId, ba.AvailBalance.String(), ba.TotalBalance.String(), ba.EarmarkWithdraw.String(), amt.String())
	}

	resp_list := match_api.ResponseList{}

	wresp := match_api.WithdrawResp{}
	wresp.WithdrawId = req.WithdrawId
	wresp.DbId = req.DbId
	wresp.Status = match_api.WithdrawStatus_WITHDRAW_STATUS_DONE

	if req.WithdrawOutcome == match_api.WithdrawOutcome_WITHDRAW_OUTCOME_SUCCESS {
		log.Debug().Msgf("balance account detail %+v", ba)
		ba.TotalBalance = ba.TotalBalance.Sub(amt)
		if !ba.EarmarkWithdraw.IsZero() {
			//reverse earmark
			ba.EarmarkWithdraw = ba.EarmarkWithdraw.Sub(amt)
			send_release_withdraw(req, &wresp, ba, amt, &resp_list)
		}
		send_balance_update_withdraw(req, &wresp, ba, amt, &resp_list)
	} else {
		if !ba.EarmarkWithdraw.IsZero() {
			ba.AvailBalance = ba.AvailBalance.Add(amt)
			ba.EarmarkWithdraw = ba.EarmarkWithdraw.Sub(amt)
			send_release_withdraw(req, &wresp, ba, amt, &resp_list)
		}
	}

	resp_ := match_api.Response{}
	resp_.Response = &match_api.Response_Withdraw{Withdraw: &wresp}
	resp_list.Responses = append(resp_list.Responses, &resp_)
	if session != nil {
		aeron_ov_svc_send(session, &resp_list)
	}
	if log_balance {
		log.Info().Msgf("[BALANCE_AFTER] process_withdraw withdraw_id=%s balanceAccountId=%s avail=%s total=%s, earmarkWithdraw=%s, total=%s",
			req.WithdrawId, ba.BalanceAccountId, ba.AvailBalance.String(), ba.TotalBalance.String(), ba.EarmarkWithdraw.String(), amt.String())
	}

	aeron_ov_out_send(&resp_list, "accepted_withdraw")

}

func send_release_withdraw(req *match_api.WithdrawReq, withdrawResp *match_api.WithdrawResp, ba *BalanceAccount, amt decimal.Decimal, resp_list *match_api.ResponseList) {
	bu := match_api.BalanceUpdate{}
	bu.BalanceAccountId = ba.BalanceAccountId
	bu.AssetSymbol = ba.AssetSymbol
	bu.WithdrawalId = req.WithdrawId
	bu.Ref = "Withdrawal " + req.WithdrawId
	bu.UpdateType = match_api.BalanceUpdateType_BALANCE_UPDATE_AVAIL_ONLY
	bu.UpdateAmount = amt.ToProtoAlloc()
	bu.BalanceAvail = ba.AvailBalance.ToProtoAlloc()
	bu.BalanceTotal = ba.TotalBalance.ToProtoAlloc()
	bu.Scenario = match_api.Scenario_SCENARIO_RELEASE_WITHDRAW
	bu.ScenarioType = match_api.ScenarioType_SCENARIO_TYPE_BALANCE
	bu.BalanceUpdateId = new_rand_balance_update_id()
	bu.Time = uint64(GetClusterTimeUnixNano())
	bu.PositionKeepingInfo = nil
	bu.NegUpdateAmount = false
	bu.DepositWithdrawalCost = req.Price
	bu.Withdraw = withdrawResp
	if req.PkEnabled {
		bu.PositionKeepingInfo = &match_api.PositionKeepingInfo{
			PkEnabled:             req.PkEnabled,
			ContractRef:           req.ContractId,
			Qty:                   req.Amount,
			Price:                 req.Price,
			ServiceAccountId:      req.ServiceAccountId,
			WdIncludeToRealizePnl: req.WdIncludeToRealizePnl,
		}
	}
	resp := &match_api.Response{}
	resp.Response = &match_api.Response_BalanceUpdate{BalanceUpdate: &bu}
	resp_list.Responses = append(resp_list.Responses, resp)
}

func send_balance_update_withdraw(req *match_api.WithdrawReq, withdrawResp *match_api.WithdrawResp, ba *BalanceAccount, amt decimal.Decimal, resp_list *match_api.ResponseList) {
	bu := match_api.BalanceUpdate{}
	bu.BalanceAccountId = ba.BalanceAccountId
	bu.AssetSymbol = ba.AssetSymbol
	bu.WithdrawalId = req.WithdrawId
	bu.Ref = "Withdrawal " + req.WithdrawId
	bu.UpdateType = match_api.BalanceUpdateType_BALANCE_UPDATE_TOTAL_AVAIL
	bu.UpdateAmount = amt.ToProtoAlloc()
	bu.NegUpdateAmount = true
	bu.BalanceAvail = ba.AvailBalance.ToProtoAlloc()
	bu.BalanceTotal = ba.TotalBalance.ToProtoAlloc()
	bu.Scenario = match_api.Scenario_SCENARIO_WITHDRAWAL
	bu.ScenarioType = match_api.ScenarioType_SCENARIO_TYPE_BALANCE
	bu.BalanceUpdateId = new_rand_balance_update_id()
	bu.Time = uint64(GetClusterTimeUnixNano())
	bu.PositionKeepingInfo = nil
	bu.Withdraw = withdrawResp
	//pk
	if req.PkEnabled {
		bu.PositionKeepingInfo = &match_api.PositionKeepingInfo{
			PkEnabled:             req.PkEnabled,
			ContractRef:           req.ContractId,
			Qty:                   req.Amount,
			Price:                 req.Price,
			ServiceAccountId:      req.ServiceAccountId,
			WdIncludeToRealizePnl: req.WdIncludeToRealizePnl,
		}
	}
	resp := &match_api.Response{}
	resp.Response = &match_api.Response_BalanceUpdate{BalanceUpdate: &bu}
	resp_list.Responses = append(resp_list.Responses, resp)
}

func sendWithdrawEarmarkErrorResponse(
	req *match_api.WithdrawEarmarkReq,
	session cluster.ClientSession,
	errorCode, remark string,
) {
	wdResp := match_api.WithdrawEarmarkResp{
		WithdrawId: req.WithdrawId,
		Status:     match_api.WithdrawStatus_WITHDRAW_STATUS_ERROR,
		ErrorCode:  errorCode,
		Remark:     remark,
		DbId:       req.DbId,
		RequestId:  req.RequestId,
	}

	bu := match_api.BalanceUpdate{
		BalanceAccountId: req.BalanceAccountId,
		AssetSymbol:      req.ContractId,
		WithdrawalId:     req.WithdrawId,
		Ref:              "Withdraw " + req.WithdrawId,
		UpdateType:       match_api.BalanceUpdateType_REJECT_BALANCE_UPDATE,
		ErrorCode:        errorCode,
		Remark:           remark,
		WithdrawEarmark:  &wdResp,
	}

	resp := &match_api.Response{
		Response: &match_api.Response_BalanceUpdate{BalanceUpdate: &bu},
	}
	respList := match_api.ResponseList{
		Responses: []*match_api.Response{resp},
	}
	aeron_ov_out_send(&respList, "reject_withdraw_earmark")

	if session != nil {
		sessionResp := &match_api.Response{
			Response: &match_api.Response_WithdrawEarmark{WithdrawEarmark: &wdResp},
		}
		sessionRespList := match_api.ResponseList{
			Responses: []*match_api.Response{sessionResp},
		}
		aeron_ov_svc_send(session, &sessionRespList)
	}
}

func sendWithdrawEarmarkSuccessResponse(
	req *match_api.WithdrawEarmarkReq,
	ba *BalanceAccount,
	amt decimal.Decimal,
	session cluster.ClientSession,
) {
    wresp := match_api.WithdrawEarmarkResp{
        WithdrawId: req.WithdrawId,
        Status:     match_api.WithdrawStatus_WITHDRAW_STATUS_PENDING,
        DbId:       req.DbId,
		RequestId: req.RequestId,
    }

    bu := match_api.BalanceUpdate{
        BalanceAccountId:     ba.BalanceAccountId,
        AssetSymbol:          ba.AssetSymbol,
        WithdrawalId:         req.WithdrawId,
        Ref:                  "Withdrawal " + req.WithdrawId,
        UpdateType:           match_api.BalanceUpdateType_BALANCE_UPDATE_AVAIL_ONLY,
        UpdateAmount:         amt.ToProtoAlloc(),
        NegUpdateAmount:      true,
        BalanceAvail:         ba.AvailBalance.ToProtoAlloc(),
        BalanceTotal:         ba.TotalBalance.ToProtoAlloc(),
        Scenario:             match_api.Scenario_SCENARIO_WITHDRAWAL,
        ScenarioType:         match_api.ScenarioType_SCENARIO_TYPE_BALANCE,
        BalanceUpdateId:      new_rand_balance_update_id(),
        Time:                 uint64(GetClusterTimeUnixNano()),
        PositionKeepingInfo:  nil,
        DepositWithdrawalCost: req.Price,
        WithdrawEarmark:      &wresp,
    }

    resp := &match_api.Response{
        Response: &match_api.Response_BalanceUpdate{BalanceUpdate: &bu},
    }
    respList := match_api.ResponseList{
        Responses: []*match_api.Response{resp},
    }
    aeron_ov_out_send(&respList, "withdraw_earmark_success")

    if session != nil {
        sessionResp := &match_api.Response{
            Response: &match_api.Response_WithdrawEarmark{WithdrawEarmark: &wresp},
        }
        sessionRespList := match_api.ResponseList{
            Responses: []*match_api.Response{sessionResp},
        }
        aeron_ov_svc_send(session, &sessionRespList)
    }
}

func process_withdraw_earmark(req *match_api.WithdrawEarmarkReq, session cluster.ClientSession) {
	log.Debug().Msgf("process_withdraw_earmark %v", req)

	if _, ok := holding_accounts[req.AccountId]; !ok {
		log.Debug().Msgf("reject withdraw earmark %s. reason account %s not found", req.WithdrawId, req.AccountId)
		sendWithdrawEarmarkErrorResponse(req, session, "13400", "Asset account cannot be found")
		return
	}

	ba := get_balance_account(req.BalanceAccountId)
	amt := decimal.NewFromProto(*req.Amount)

	if log_balance {
		log.Info().Msgf("[BALANCE_BEFORE] process_withdraw_earmark withdraw_id=%s balanceAccountId=%s avail=%s, total=%s earmarkWithdraw=%s txnAmt=%s",
			req.WithdrawId, ba.BalanceAccountId, ba.AvailBalance.String(), ba.TotalBalance.String(), ba.EarmarkWithdraw.String(), amt.String())
	}

	if ba.AvailBalance.Cmp(amt) < 0 {
		msg := fmt.Sprintf("insufficient balance: %s < %s", ba.AvailBalance.String(), amt)
		log.Warn().Msgf(msg)
		sendWithdrawEarmarkErrorResponse(req, session, "21420", "Insufficient balance")
		return
	}

	// Update balances (success case)
	ba.AvailBalance = ba.AvailBalance.Sub(amt)
	ba.EarmarkWithdraw = ba.EarmarkWithdraw.Add(amt)

	sendWithdrawEarmarkSuccessResponse(req, ba, amt, session)

	if log_balance {
		log.Info().Msgf("[BALANCE_AFTER] process_withdraw_earmark withdraw_id=%s balanceAccountId=%s avail=%s, total=%s earmarkWithdraw=%s txnAmt=%s",
			req.WithdrawId, ba.BalanceAccountId, ba.AvailBalance.String(), ba.TotalBalance.String(), ba.EarmarkWithdraw.String(), amt.String())
	}
}

func process_clear_balance(req *match_api.ClearBalanceReq) {
	log.Debug().Msgf("process_clear_balance %v", req)
	ba := get_balance_account(req.BalanceAccountId)
	if log_balance {
		log.Info().Msgf("[BALANCE_BEFORE] process_clear_balance balanceAccountId=%s avail=%s, total=%s",
			req.BalanceAccountId, ba.AvailBalance.String(), ba.TotalBalance.String())
	}
	ba.AvailBalance = decimal.Zero
	ba.TotalBalance = decimal.Zero
	ba.PendingIN = decimal.Zero
	ba.PendingOUT = decimal.Zero
	ba.EarmarkWithdraw = decimal.Zero
	if log_balance {
		log.Info().Msgf("[BALANCE_AFTER] process_clear_balance balanceAccountId=%s avail=%s, total=%s earmark_withdraw=%s",
			req.BalanceAccountId, ba.AvailBalance.String(), ba.TotalBalance.String(), ba.EarmarkWithdraw.String())
	}
}

func process_disconnect(req *match_api.DisconnectReq) {
	log.Debug().Msgf("process_disconnect %v", req)
	//dis := match_api.DisconnectReq{}
	//dis.SenderCompId = req.SenderCompId
	//dis.Source = req.Source
	dis := req
	mr := match_api.Request{}
	mr.Request = &match_api.Request_Disconnect{Disconnect: dis}
	aeron_match_in_send(&mr)
}

func process_match_response(resp *match_api.Response) {
	// the check to prevent duplicated log
	if _, ok := resp.Response.(*match_api.Response_ExecReport); !ok {
		log.Debug().Msgf("process_match_response: %v", resp)
	}

	switch m := resp.Response.(type) {
	case *match_api.Response_ExecReport:
		err := bondService.ProcessER(m.ExecReport, process_exec_report)
		if err != nil {
			log.Error().Err(err).Msg("Failed to process ER")
		}
	case *match_api.Response_PriceUpdate:
		if m.PriceUpdate.GetFromService() != "sor" && m.PriceUpdate.GetPrice() != nil {
			process_price_update(m.PriceUpdate)
		}
	case *match_api.Response_ClearPrice:
		process_clear_price(m.ClearPrice.Symbol)
	case *match_api.Response_PsParentOrderExecReport:
		// if enable_print_metrics {
		// 	if m.PsParentOrderExecReport.OrderStatus == match_api.PsParentOrderStatus_PS_PARENT_ORDER_STATUS_PENDING && m.PsParentOrderExecReport.PsSenderService == match_api.PsSenderService_PS_SENDER_SERVICE_SOR {
		// 		latency := time.Now().UnixMilli() - int64(m.PsParentOrderExecReport.RequestReceivedTime)
		// 		log.Debug().Msgf("received time: %d", int64(m.PsParentOrderExecReport.RequestReceivedTime))
		// 		log.Debug().Msgf("latency: %d ms", latency)
		// 		if latency > 100000 {
		// 			log.Warn().Msgf("latency too big: %d ms, %d ", latency, int64(m.PsParentOrderExecReport.RequestReceivedTime))
		// 		} else {
		// 			err := latency_hist.RecordValue(latency)
		// 			if err != nil {
		// 				log.Error().Msgf("latency_hist.RecordValue failed: %v", err)
		// 			}
		// 		}

		// 	}
		// }
		metrics.PsParentOrderExecReport++
		processPsParentOrderExecReport(m.PsParentOrderExecReport)
	case *match_api.Response_PsChildOrderExecReport:
		processPsChildOrderExecReport(m.PsChildOrderExecReport)

	}

	metrics.ResponsesProcessed++
}

func getBalanceAccountByMarketCodeAndAhaNumber(symbol string, marketCode string, holdingAccountNumber string) (string, string, error) {
	mkt := markets[marketCode]
	operatorCode := mkt.OperatorCode
	_, tickerSym := utils.SplitMarket(symbol)
	baseSym, quoteSym := utils.SplitSym(tickerSym)
	baseAsset := mkt.Assets[baseSym]
	if baseAsset == nil {
		return "", "", fmt.Errorf("base asset not found for symbol=%s", baseSym)
	}
	quoteAsset := mkt.Assets[quoteSym]
	if quoteAsset == nil {
		return "", "", fmt.Errorf("quote asset not found for symbol=%s", quoteSym)
	}
	baseBalanceAccountId := operatorCode + ":" + holdingAccountNumber + ":" + baseAsset.BalanceAccountCode
	quoteBalanceAccountId := operatorCode + ":" + holdingAccountNumber + ":" + quoteAsset.BalanceAccountCode
	return baseBalanceAccountId, quoteBalanceAccountId, nil
}

func process_clear_price(symbol string) {
	log.Info().Msgf("[CLEAR_OV_PRICE] For symbol=%s", symbol)
	ob := &OrderBook{
		Symbol:      symbol,
		BidLevels:   btree.NewG[*PriceLevel](2, func(a, b *PriceLevel) bool { return a.Price.LessThan(b.Price) }),
		AskLevels:   btree.NewG[*PriceLevel](2, func(a, b *PriceLevel) bool { return a.Price.LessThan(b.Price) }),
		open_orders: make(map[string]*OpenOrder),
	}
	orderbooks[symbol] = ob
}

func process_exec_report_new(rep *match_api.ExecReport) error {
	ob := get_orderbook(rep.Symbol)
	order := &OpenOrder{
		Symbol:           rep.Symbol,
		ClOrdId:          rep.ClOrdId,
		OrderId:          rep.OrderId,
		OpenQty:          decimal.NewFromProto(*rep.OpenQty),
		Price:            decimal.NewFromProto(*rep.Price),
		HoldingAccountId: rep.HoldingAccountId,
		ServiceAccountId: rep.ServiceAccountId,
		Side:             rep.Side,
		SenderCompId:     rep.SenderCompId,
		MarketCode:       rep.MarketCode,
		InterestAmt:      decimal.Zero,
	}
	ob.open_orders[rep.ClOrdId] = order
	log.Debug().Str("ClOrdId", order.ClOrdId).Str("ExecType", "EXEC_TYPE_NEW").Msg("Added order to open orders")

	return nil
}

func process_exec_report_trade(rep *match_api.ExecReport) error {
	pk_enabled := false
	if mkt, ok := markets[rep.MarketCode]; ok {
		pk_enabled = mkt.PositionKeeping.Bool
		if mkt.EnableExtSettlementConfirm {
			process_exec_report_trade_pending(rep, pk_enabled)
			return nil
		}
	}
	var receive_ba *BalanceAccount
	var receive_amt decimal.Decimal
	var spend_ba *BalanceAccount
	var spend_amt decimal.Decimal
	var scenario_receive, scenario_spend, scenario_fees, scenario_tax match_api.Scenario
	var scenarioEarmark, scenarioEarmarkFees, scenarioEarmarkTax match_api.Scenario
	var last_fill_qty = decimal.NewFromProto(*rep.LastFillQty)
	var last_fill_price = decimal.NewFromProto(*rep.LastFillPrice)

	if rep.Side == match_api.Side_SIDE_BUY {
		receive_ba = balance_accounts[rep.BaseBalanceAccountId]
		if receive_ba == nil {
			log.Info().Msgf("balanceAccId to credit not found. going to create new %s", rep.BaseBalanceAccountId)
			splits := strings.Split(rep.BaseBalanceAccountId, ":")
			receive_ba = &BalanceAccount{
				BalanceAccountId: rep.BaseBalanceAccountId,
				HoldingAccountId: splits[1],
				AssetSymbol:      splits[2],
				AvailBalance:     decimal.Zero,
				TotalBalance:     decimal.Zero,
				PendingIN:        decimal.Zero,
				PendingOUT:       decimal.Zero,
			}
			balance_accounts[rep.BaseBalanceAccountId] = receive_ba
		}
		receive_amt = last_fill_qty
		spend_ba = balance_accounts[rep.QuoteBalanceAccountId]
		if spend_ba == nil {
			log.Error().Msgf("[*CLOB*] process_exec_report_trade quote account is nil for rep.QuoteBalanceAccountId=%s", rep.QuoteBalanceAccountId)
			return nil
		}
		spend_amt = last_fill_price.Mul(last_fill_qty)
		scenario_receive = match_api.Scenario_SCENARIO_CONFIRM_BUY_BASE
		scenario_spend = match_api.Scenario_SCENARIO_CONFIRM_BUY_QUOTE
		if rep.MakerTaker {
			scenario_fees = match_api.Scenario_SCENARIO_CONFIRM_FEES_TAKER_BUY
			scenario_tax = match_api.Scenario_SCENARIO_CONFIRM_TAX_TAKER_BUY
		} else {
			scenario_fees = match_api.Scenario_SCENARIO_CONFIRM_FEES_MAKER_BUY
			scenario_tax = match_api.Scenario_SCENARIO_CONFIRM_TAX_MAKER_BUY
		}
		scenarioEarmark = match_api.Scenario_SCENARIO_REVERSE_EARMARK_BUY
		scenarioEarmarkFees = match_api.Scenario_SCENARIO_REVERSE_EARMARK_BUY_FEES
		scenarioEarmarkTax = match_api.Scenario_SCENARIO_REVERSE_EARMARK_BUY_TAX
	} else {
		receive_ba = balance_accounts[rep.QuoteBalanceAccountId]
		if receive_ba == nil {
			log.Debug().Msgf("[*CLOB*] process_exec_report_trade balanceAccId to credit not found. going to create new %s", rep.QuoteBalanceAccountId)
			splits := strings.Split(rep.QuoteBalanceAccountId, ":")
			receive_ba = &BalanceAccount{
				BalanceAccountId: rep.QuoteBalanceAccountId,
				HoldingAccountId: splits[1],
				AssetSymbol:      splits[2],
				AvailBalance:     decimal.Zero,
				TotalBalance:     decimal.Zero,
				PendingIN:        decimal.Zero,
				PendingOUT:       decimal.Zero,
			}
			balance_accounts[rep.QuoteBalanceAccountId] = receive_ba
		}
		receive_amt = last_fill_price.Mul(last_fill_qty)
		spend_ba = balance_accounts[rep.BaseBalanceAccountId]
		if spend_ba == nil {
			log.Error().Msgf("[*CLOB*] process_exec_report_trade base account is nil for rep.BaseBalanceAccountId=%s", rep.BaseBalanceAccountId)
			return nil
		}
		spend_amt = last_fill_qty
		scenario_receive = match_api.Scenario_SCENARIO_CONFIRM_SELL_QUOTE
		scenario_spend = match_api.Scenario_SCENARIO_CONFIRM_SELL_BASE
		if rep.MakerTaker {
			scenario_fees = match_api.Scenario_SCENARIO_CONFIRM_FEES_TAKER_SELL
			scenario_tax = match_api.Scenario_SCENARIO_CONFIRM_TAX_TAKER_SELL
		} else {
			scenario_fees = match_api.Scenario_SCENARIO_CONFIRM_FEES_MAKER_SELL
			scenario_tax = match_api.Scenario_SCENARIO_CONFIRM_TAX_MAKER_SELL
		}
		scenarioEarmark = match_api.Scenario_SCENARIO_REVERSE_EARMARK_SELL
		scenarioEarmarkFees = match_api.Scenario_SCENARIO_REVERSE_EARMARK_SELL_FEES
		scenarioEarmarkTax = match_api.Scenario_SCENARIO_REVERSE_EARMARK_SELL_TAX
	}
	trade_id := rep.LastTradeId
	maker_taker_type := "maker"
	if rep.MakerTaker {
		maker_taker_type = "taker"
	}
	update_balance(rep.OrderId, rep.MarketCode, receive_ba, receive_amt, match_api.BalanceUpdateType_BALANCE_UPDATE_TOTAL_AVAIL, BALANCE_REFERENCE_TRADE+trade_id, false, match_api.ScenarioType_SCENARIO_TYPE_BALANCE, scenario_receive,
		pk_enabled, rep.ServiceAccountId, rep.Symbol, rep.Side, rep.LastFillQty, rep.LastFillPrice, rep.FillFeeAmt, rep.FillTaxAmt, maker_taker_type)
	update_balance_neg(rep.OrderId, rep.MarketCode, spend_ba, spend_amt, match_api.BalanceUpdateType_BALANCE_UPDATE_TOTAL_AVAIL, BALANCE_REFERENCE_TRADE+trade_id, false, match_api.ScenarioType_SCENARIO_TYPE_BALANCE, scenario_spend,
		false, "", "", rep.Side, nil, nil, nil, nil, maker_taker_type)
	update_balance(rep.OrderId, rep.MarketCode, spend_ba, spend_amt, match_api.BalanceUpdateType_BALANCE_UPDATE_AVAIL_ONLY, BALANCE_REFERENCE_TRADE+trade_id, false, match_api.ScenarioType_SCENARIO_TYPE_BALANCE, scenarioEarmark,
		false, "", "", rep.Side, nil, nil, nil, nil, "")

	fee_amt := decimal.NewFromProto(*rep.FillFeeAmt)
	if !fee_amt.IsZero() {
		fee_ba := balance_accounts[rep.QuoteBalanceAccountId]
		// skip sell fee feature,
		// case where by Sell fill fee > order fee earmark and < traded amount, and Skip sell is turned on halfway.
		// fee is took from earmark + traded quote amount
		// FillFeeFromEarmarkAmt && FillFeeFromTradeAmt only have value if Sell fill fee > order fee earmark and < traded amount, and Skip sell is turned on halfway.
		charge_fee_from_trade_amt := decimal.Zero
		if rep.FillFeeFromTradeAmt != nil {
			charge_fee_from_trade_amt = decimal.NewFromProto(*rep.FillFeeFromTradeAmt)
		}
		charge_eamark_fee_amt := decimal.Zero
		if rep.FillFeeFromEarmarkAmt != nil {
			charge_eamark_fee_amt = decimal.NewFromProto(*rep.FillFeeFromEarmarkAmt)
		}
		if !charge_fee_from_trade_amt.IsZero() && !charge_eamark_fee_amt.IsZero() {
			update_balance_neg(rep.OrderId, rep.MarketCode, fee_ba, charge_fee_from_trade_amt, match_api.BalanceUpdateType_BALANCE_UPDATE_TOTAL_AVAIL, BALANCE_REFERENCE_TRADE_FEE+trade_id, false, match_api.ScenarioType_SCENARIO_TYPE_FEES, scenario_fees,
				pk_enabled, rep.ServiceAccountId, rep.Symbol, rep.Side, rep.LastFillQty, rep.LastFillPrice, nil, nil, maker_taker_type)
			update_balance_neg(rep.OrderId, rep.MarketCode, fee_ba, charge_eamark_fee_amt, match_api.BalanceUpdateType_BALANCE_UPDATE_TOTAL_AVAIL, BALANCE_REFERENCE_TRADE_FEE+trade_id, false, match_api.ScenarioType_SCENARIO_TYPE_FEES, scenario_fees,
				pk_enabled, rep.ServiceAccountId, rep.Symbol, rep.Side, rep.LastFillQty, rep.LastFillPrice, nil, nil, maker_taker_type)

			update_balance(rep.OrderId, rep.MarketCode, fee_ba, charge_eamark_fee_amt, match_api.BalanceUpdateType_BALANCE_UPDATE_AVAIL_ONLY, BALANCE_REFERENCE_TRADE_FEE+trade_id, false, match_api.ScenarioType_SCENARIO_TYPE_FEES, scenarioEarmarkFees,
				false, "", "", rep.Side, nil, nil, nil, nil, "")
		} else {
			update_balance_neg(rep.OrderId, rep.MarketCode, fee_ba, fee_amt, match_api.BalanceUpdateType_BALANCE_UPDATE_TOTAL_AVAIL, BALANCE_REFERENCE_TRADE_FEE+trade_id, false, match_api.ScenarioType_SCENARIO_TYPE_FEES, scenario_fees,
				pk_enabled, rep.ServiceAccountId, rep.Symbol, rep.Side, rep.LastFillQty, rep.LastFillPrice, nil, nil, maker_taker_type)

			// when skip sell we order don't have fee earmark. hence no need to reverse earmark fee
			earmark_fee := decimal.NewFromProto(*rep.EarmarkFeeAmt)
			if !earmark_fee.IsZero() {
				update_balance(rep.OrderId, rep.MarketCode, fee_ba, fee_amt, match_api.BalanceUpdateType_BALANCE_UPDATE_AVAIL_ONLY, BALANCE_REFERENCE_TRADE_FEE+trade_id, false, match_api.ScenarioType_SCENARIO_TYPE_FEES, scenarioEarmarkFees,
					false, "", "", rep.Side, nil, nil, nil, nil, "")
			}
		}
	}

	tax_amt := decimal.NewFromProto(*rep.FillTaxAmt)
	if !tax_amt.IsZero() {
		tax_ba := balance_accounts[rep.QuoteBalanceAccountId]

		// skip sell fee feature,
		// case where by Sell fill fee > order fee earmark and < traded amount, and Skip sell is turned on halfway.
		// fee is took from earmark + traded quote amount
		// FillFeeFromEarmarkAmt && FillFeeFromTradeAmt only have value if Sell fill fee > order fee earmark and < traded amount, and Skip sell is turned on halfway.
		charge_tax_from_trade_amt := decimal.Zero
		if rep.FillTaxFromTradeAmt != nil {
			charge_tax_from_trade_amt = decimal.NewFromProto(*rep.FillTaxFromTradeAmt)
		}
		charge_eamark_tax_amt := decimal.Zero
		if rep.FillTaxFromEarmarkAmt != nil {
			charge_eamark_tax_amt = decimal.NewFromProto(*rep.FillTaxFromEarmarkAmt)
		}
		if !charge_tax_from_trade_amt.IsZero() && !charge_eamark_tax_amt.IsZero() {
			update_balance_neg(rep.OrderId, rep.MarketCode, tax_ba, charge_tax_from_trade_amt, match_api.BalanceUpdateType_BALANCE_UPDATE_TOTAL_AVAIL, BALANCE_REFERENCE_TRADE_TAX+trade_id, false, match_api.ScenarioType_SCENARIO_TYPE_TAX, scenario_tax,
				pk_enabled, rep.ServiceAccountId, rep.Symbol, rep.Side, rep.LastFillQty, rep.LastFillPrice, nil, nil, maker_taker_type)
			update_balance_neg(rep.OrderId, rep.MarketCode, tax_ba, charge_eamark_tax_amt, match_api.BalanceUpdateType_BALANCE_UPDATE_TOTAL_AVAIL, BALANCE_REFERENCE_TRADE_TAX+trade_id, false, match_api.ScenarioType_SCENARIO_TYPE_TAX, scenario_tax,
				pk_enabled, rep.ServiceAccountId, rep.Symbol, rep.Side, rep.LastFillQty, rep.LastFillPrice, nil, nil, maker_taker_type)

			update_balance(rep.OrderId, rep.MarketCode, tax_ba, charge_eamark_tax_amt, match_api.BalanceUpdateType_BALANCE_UPDATE_AVAIL_ONLY, BALANCE_REFERENCE_TRADE_TAX+trade_id, false, match_api.ScenarioType_SCENARIO_TYPE_TAX, scenarioEarmarkTax,
				false, "", "", rep.Side, nil, nil, nil, nil, "")

		} else {
			update_balance_neg(rep.OrderId, rep.MarketCode, tax_ba, tax_amt, match_api.BalanceUpdateType_BALANCE_UPDATE_TOTAL_AVAIL, BALANCE_REFERENCE_TRADE_TAX+trade_id, false, match_api.ScenarioType_SCENARIO_TYPE_TAX, scenario_tax,
				pk_enabled, rep.ServiceAccountId, rep.Symbol, rep.Side, rep.LastFillQty, rep.LastFillPrice, nil, nil, maker_taker_type)

			// when skip sell we order don't have tax earmark. hence no need to reverse earmark tax
			earmark_tax := decimal.NewFromProto(*rep.EarmarkTaxAmt)
			if !earmark_tax.IsZero() {
				update_balance(rep.OrderId, rep.MarketCode, tax_ba, tax_amt, match_api.BalanceUpdateType_BALANCE_UPDATE_AVAIL_ONLY, BALANCE_REFERENCE_TRADE_TAX+trade_id, false, match_api.ScenarioType_SCENARIO_TYPE_TAX, scenarioEarmarkTax,
					false, "", "", rep.Side, nil, nil, nil, nil, "")
			}
		}
	}
	ob := get_orderbook(rep.Symbol)
	openOrder := ob.open_orders[rep.ClOrdId]
	if openOrder != nil {
		openOrder.OpenQty = decimal.NewFromProto(*rep.OpenQty)
	}
	if rep.OrdStatus == match_api.OrdStatus_ORD_STATUS_FILLED {
		earmarkAmount := decimal.NewFromProto(*rep.EarmarkAmt)
		earmark_fee := decimal.NewFromProto(*rep.EarmarkFeeAmt)
		earmark_tax := decimal.NewFromProto(*rep.EarmarkTaxAmt)
		cumFillQuantity := decimal.NewFromProto(*rep.FillQty)
		cumFillAmount := decimal.NewFromProto(*rep.CumFillAmt)
		cum_fill_fee := decimal.NewFromProto(*rep.CumFillFeeAmt)
		cum_fill_tax := decimal.NewFromProto(*rep.CumFillTaxAmt)
		var remain_earmark_amt decimal.Decimal
		var remain_earmark_fee decimal.Decimal
		var remain_earmark_tax decimal.Decimal
		var earmarkBalance *BalanceAccount
		var earmark_fee_ba *BalanceAccount
		var earmark_tax_ba *BalanceAccount
		if rep.Side == match_api.Side_SIDE_BUY {
			remain_earmark_amt = earmarkAmount.Sub(cumFillAmount)
			earmarkBalance = balance_accounts[rep.QuoteBalanceAccountId]
			if earmarkBalance == nil {
				panic(QUOTE_ACCOUNT_IS_NIL_ERRORR)
			}
			scenarioEarmark = match_api.Scenario_SCENARIO_RETURN_EARMARK_BUY
			scenarioEarmarkFees = match_api.Scenario_SCENARIO_RETURN_EARMARK_BUY_FEES
			scenarioEarmarkTax = match_api.Scenario_SCENARIO_RETURN_EARMARK_BUY_TAX
		} else if rep.Side == match_api.Side_SIDE_SELL {
			remain_earmark_amt = earmarkAmount.Sub(cumFillQuantity)
			earmarkBalance = balance_accounts[rep.BaseBalanceAccountId]
			if earmarkBalance == nil {
				panic(BASE_ACCOUNT_IS_NIL_ERRORR)
			}
			scenarioEarmark = match_api.Scenario_SCENARIO_RETURN_EARMARK_SELL
			scenarioEarmarkFees = match_api.Scenario_SCENARIO_RETURN_EARMARK_SELL_FEES
			scenarioEarmarkTax = match_api.Scenario_SCENARIO_RETURN_EARMARK_SELL_TAX
		}

		if !remain_earmark_amt.IsZero() {
			update_balance(rep.OrderId, rep.MarketCode, earmarkBalance, remain_earmark_amt, match_api.BalanceUpdateType_BALANCE_UPDATE_AVAIL_ONLY, BALANCE_REFERENCE_RELEASE_EARMARK+rep.OrderId, false, match_api.ScenarioType_SCENARIO_TYPE_BALANCE, scenarioEarmark,
				false, "", "", rep.Side, nil, nil, nil, nil, "")
		}

		earmark_fee_ba = balance_accounts[rep.QuoteBalanceAccountId]
		if earmark_fee_ba == nil {
			panic(QUOTE_ACCOUNT_IS_NIL_ERRORR)
		}
		earmark_tax_ba = earmark_fee_ba
		if earmark_fee.GreaterThan(cum_fill_fee) {
			remain_earmark_fee = earmark_fee.Sub(cum_fill_fee)
			if !remain_earmark_fee.IsZero() {
				update_balance(rep.OrderId, rep.MarketCode, earmark_fee_ba, remain_earmark_fee, match_api.BalanceUpdateType_BALANCE_UPDATE_AVAIL_ONLY, BALANCE_REFERENCE_RELEASE_EARMARK+rep.OrderId, false, match_api.ScenarioType_SCENARIO_TYPE_FEES, scenarioEarmarkFees,
					false, "", "", rep.Side, nil, nil, nil, nil, "")
			}
		}
		if earmark_tax.GreaterThan(cum_fill_tax) {
			remain_earmark_tax = earmark_tax.Sub(cum_fill_tax)
			if !remain_earmark_tax.IsZero() {
				update_balance(rep.OrderId, rep.MarketCode, earmark_tax_ba, remain_earmark_tax, match_api.BalanceUpdateType_BALANCE_UPDATE_AVAIL_ONLY, BALANCE_REFERENCE_RELEASE_EARMARK+rep.OrderId, false, match_api.ScenarioType_SCENARIO_TYPE_TAX, scenarioEarmarkTax,
					false, "", "", rep.Side, nil, nil, nil, nil, "")
			}
		}
		delete(ob.open_orders, rep.ClOrdId)
		log.Debug().Str("ClOrdId", rep.ClOrdId).Msg("Deleted order from open orders")
	}

	return nil
}

func process_exec_report_reject(rep *match_api.ExecReport) error {
	var scenarioEarmark, scenario_earmark_fee, scenarioEarmarkTax match_api.Scenario
	var earmarkBalance, earmark_fee_ba, earmark_tax_ba *BalanceAccount
	earmarkAmount := decimal.NewFromProto(*rep.EarmarkAmt)
	earmark_fee := decimal.NewFromProto(*rep.EarmarkFeeAmt)
	earmark_tax := decimal.NewFromProto(*rep.EarmarkTaxAmt)
	if rep.Side == match_api.Side_SIDE_BUY {
		scenarioEarmark = match_api.Scenario_SCENARIO_REJECT_ORDER_BUY
		scenario_earmark_fee = match_api.Scenario_SCENARIO_REJECT_ORDER_BUY_FEES
		scenarioEarmarkTax = match_api.Scenario_SCENARIO_REJECT_ORDER_BUY_TAX
		earmarkBalance = balance_accounts[rep.QuoteBalanceAccountId]
		if earmarkBalance == nil {
			log.Error().Msgf(QUOTE_ACCOUNT_IS_NIL_ERRORR)
			return nil
		}
	} else if rep.Side == match_api.Side_SIDE_SELL {
		scenarioEarmark = match_api.Scenario_SCENARIO_REJECT_ORDER_SELL
		scenario_earmark_fee = match_api.Scenario_SCENARIO_REJECT_ORDER_SELL_FEES
		scenarioEarmarkTax = match_api.Scenario_SCENARIO_REJECT_ORDER_SELL_TAX
		earmarkBalance = balance_accounts[rep.BaseBalanceAccountId]
		if earmarkBalance == nil {
			log.Error().Msgf(BASE_ACCOUNT_IS_NIL_ERRORR)
			return nil
		}
	}
	earmark_fee_ba = balance_accounts[rep.QuoteBalanceAccountId]
	if earmark_fee_ba == nil {
		log.Error().Msgf(QUOTE_ACCOUNT_IS_NIL_ERRORR)
		return nil
	}
	earmark_tax_ba = earmark_fee_ba
	if !earmarkAmount.IsZero() {
		update_balance(rep.OrderId, rep.MarketCode, earmarkBalance, earmarkAmount, match_api.BalanceUpdateType_BALANCE_UPDATE_AVAIL_ONLY, BALANCE_REFERENCE_REJECR_ORDER+rep.OrderId, false, match_api.ScenarioType_SCENARIO_TYPE_BALANCE, scenarioEarmark,
			false, "", "", rep.Side, nil, nil, nil, nil, "")
	}
	if !earmark_fee.IsZero() {
		update_balance(rep.OrderId, rep.MarketCode, earmark_fee_ba, earmark_fee, match_api.BalanceUpdateType_BALANCE_UPDATE_AVAIL_ONLY, BALANCE_REFERENCE_REJECR_ORDER+rep.OrderId, false, match_api.ScenarioType_SCENARIO_TYPE_FEES, scenario_earmark_fee,
			false, "", "", rep.Side, nil, nil, nil, nil, "")
	}
	if !earmark_tax.IsZero() {
		update_balance(rep.OrderId, rep.MarketCode, earmark_tax_ba, earmark_tax, match_api.BalanceUpdateType_BALANCE_UPDATE_AVAIL_ONLY, BALANCE_REFERENCE_REJECR_ORDER+rep.OrderId, false, match_api.ScenarioType_SCENARIO_TYPE_TAX, scenarioEarmarkTax,
			false, "", "", rep.Side, nil, nil, nil, nil, "")
	}

	return nil
}

func process_exec_report_cancel(rep *match_api.ExecReport) error {
	var scenarioEarmark, scenario_earmark_fee, scenarioEarmarkTax match_api.Scenario
	var remain_earmark_amt, remain_earmark_fee, remain_earmark_tax decimal.Decimal
	var earmarkBalance, earmark_fee_ba, earmark_tax_ba *BalanceAccount
	earmarkAmount := decimal.NewFromProto(*rep.EarmarkAmt)
	earmark_fee := decimal.NewFromProto(*rep.EarmarkFeeAmt)
	earmark_tax := decimal.NewFromProto(*rep.EarmarkTaxAmt)
	cumFillQuantity := decimal.NewFromProto(*rep.FillQty)
	cumFillAmount := decimal.NewFromProto(*rep.CumFillAmt)
	cum_fill_fee := decimal.NewFromProto(*rep.CumFillFeeAmt)
	cum_fill_tax := decimal.NewFromProto(*rep.CumFillTaxAmt)
	if rep.Side == match_api.Side_SIDE_BUY {
		scenarioEarmark = match_api.Scenario_SCENARIO_CANCEL_ORDER_BUY
		scenario_earmark_fee = match_api.Scenario_SCENARIO_CANCEL_ORDER_BUY_FEES
		scenarioEarmarkTax = match_api.Scenario_SCENARIO_CANCEL_ORDER_BUY_TAX
		remain_earmark_amt = earmarkAmount.Sub(cumFillAmount)
		earmarkBalance = balance_accounts[rep.QuoteBalanceAccountId]
		if earmarkBalance == nil {
			log.Error().Msgf(QUOTE_ACCOUNT_IS_NIL_ERRORR)
			return nil
		}
	} else if rep.Side == match_api.Side_SIDE_SELL {
		scenarioEarmark = match_api.Scenario_SCENARIO_CANCEL_ORDER_SELL
		scenario_earmark_fee = match_api.Scenario_SCENARIO_CANCEL_ORDER_SELL_FEES
		scenarioEarmarkTax = match_api.Scenario_SCENARIO_CANCEL_ORDER_SELL_TAX
		remain_earmark_amt = earmarkAmount.Sub(cumFillQuantity)
		earmarkBalance = balance_accounts[rep.BaseBalanceAccountId]
		if earmarkBalance == nil {
			log.Error().Msgf(BASE_ACCOUNT_IS_NIL_ERRORR)
			return nil
		}
	}
	remain_earmark_fee = earmark_fee.Sub(cum_fill_fee)
	remain_earmark_tax = earmark_tax.Sub(cum_fill_tax)
	earmark_fee_ba = balance_accounts[rep.QuoteBalanceAccountId]
	if earmark_fee_ba == nil {
		log.Error().Msgf(QUOTE_ACCOUNT_IS_NIL_ERRORR)
		return nil
	}
	log.Debug().Msgf("remain_earmark_amt : %v , remain_earmark_fee : %v remain_earmark_tax : %v", remain_earmark_amt, remain_earmark_fee, remain_earmark_tax)
	earmark_tax_ba = earmark_fee_ba
	if !remain_earmark_amt.IsZero() {
		update_balance(rep.OrderId, rep.MarketCode, earmarkBalance, remain_earmark_amt, match_api.BalanceUpdateType_BALANCE_UPDATE_AVAIL_ONLY, BALANCE_REFERENCE_CANCEL_ORDER+rep.OrderId, false, match_api.ScenarioType_SCENARIO_TYPE_BALANCE, scenarioEarmark,
			false, "", "", rep.Side, nil, nil, nil, nil, "")

	}
	if !remain_earmark_fee.IsZero() {
		update_balance(rep.OrderId, rep.MarketCode, earmark_fee_ba, remain_earmark_fee, match_api.BalanceUpdateType_BALANCE_UPDATE_AVAIL_ONLY, BALANCE_REFERENCE_CANCEL_ORDER+rep.OrderId, false, match_api.ScenarioType_SCENARIO_TYPE_FEES, scenario_earmark_fee,
			false, "", "", rep.Side, nil, nil, nil, nil, "")
	}
	if !remain_earmark_tax.IsZero() {
		update_balance(rep.OrderId, rep.MarketCode, earmark_tax_ba, remain_earmark_tax, match_api.BalanceUpdateType_BALANCE_UPDATE_AVAIL_ONLY, BALANCE_REFERENCE_CANCEL_ORDER+rep.OrderId, false, match_api.ScenarioType_SCENARIO_TYPE_TAX, scenarioEarmarkTax,
			false, "", "", rep.Side, nil, nil, nil, nil, "")
	}

	ob := get_orderbook(rep.Symbol)
	delete(ob.open_orders, rep.ClOrdId)
	log.Debug().Str("ClOrdId", rep.ClOrdId).Msg("Deleted order from open orders")

	return nil
}

func process_exec_report_replaced(rep *match_api.ExecReport) error {
	ob := get_orderbook(rep.Symbol)
	delete(ob.open_orders, rep.ClOrdId)
	log.Debug().Str("ClOrdId", rep.ClOrdId).Msg("Deleted order from open orders")
	return nil
}

func process_exec_report(rep *match_api.ExecReport) error {
	log.Debug().Msgf("process ER: %v", rep)

	if tracer != nil {
		if rep.TraceId != "" {
			traceID, _ := trace.TraceIDFromHex(rep.TraceId)
			sc := trace.NewSpanContext(trace.SpanContextConfig{
				TraceID:    traceID,
				SpanID:     trace.SpanID{}, // Generate new or use valid span ID
				TraceFlags: 0x1,            // Set the sampled flag
				Remote:     false,
			})
			_, span := tracer.Start(trace.ContextWithSpanContext(context.Background(), sc), "process_exec_report")
			defer span.End()
		}
	}

	var err error

	if rep.ExecType == match_api.ExecType_EXEC_TYPE_NEW {
		err = process_exec_report_new(rep)
	} else if rep.ExecType == match_api.ExecType_EXEC_TYPE_TRADE {
		err = process_exec_report_trade(rep)
	} else if rep.ExecType == match_api.ExecType_EXEC_TYPE_CANCELED {
		err = process_exec_report_cancel(rep)
	} else if rep.ExecType == match_api.ExecType_EXEC_TYPE_REJECTED {
		err = process_exec_report_reject(rep)
	} else if rep.ExecType == match_api.ExecType_EXEC_TYPE_REPLACED {
		err = process_exec_report_replaced(rep)
	}

	return err
}

func get_orderbook(symbol string) *OrderBook {
	ob := orderbooks[symbol]
	if ob == nil {
		ob = &OrderBook{
			Symbol:      symbol,
			BidLevels:   btree.NewG[*PriceLevel](2, func(a, b *PriceLevel) bool { return a.Price.LessThan(b.Price) }),
			AskLevels:   btree.NewG[*PriceLevel](2, func(a, b *PriceLevel) bool { return a.Price.LessThan(b.Price) }),
			open_orders: make(map[string]*OpenOrder),
		}
		orderbooks[symbol] = ob
	}
	return ob
}

func process_price_update(pu *match_api.PriceUpdate) {
	log.Debug().Msgf("process_price_update: %v", pu)
	ob := get_orderbook(pu.Symbol)
	ob.Mutex.Lock()
	defer ob.Mutex.Unlock()
	price := decimal.NewFromProto(*pu.Price)
	qty := decimal.NewFromProto(*pu.Qty)
	new_level := &PriceLevel{
		Price:  price,
		Qty:    qty,
		Offset: pu.Offset,
	}

	isZeroQty := qty.IsZero()
	if pu.Side == match_api.Side_SIDE_BUY {
		level, _ := ob.BidLevels.Get(new_level)
		if level == nil || level.Offset <= pu.Offset {
			if isZeroQty && level != nil {
				ob.BidLevels.Delete(level)
			} else if !isZeroQty {
				ob.BidLevels.ReplaceOrInsert(new_level)
			}
		}
	} else {
		level, _ := ob.AskLevels.Get(new_level)
		if level == nil || level.Offset <= pu.Offset {
			if isZeroQty && level != nil {
				ob.AskLevels.Delete(level)
			} else if !isZeroQty {
				ob.AskLevels.ReplaceOrInsert(new_level)
			}
		}
	}
}

func request_price_levels() {
	log.Debug().Msg("request_price_levels")
	req := &match_api.Request{
		Request: &match_api.Request_GetPriceLevels{},
	}
	aeron_client_match_send(req)
}

func process_price_levels(resp *match_api.GetPriceLevelsResp) {
	log.Debug().Msgf("process_price_levels %+v", resp)
	for _, ob_resp := range resp.Orderbooks {
		sym := ob_resp.Symbol
		ob := orderbooks[sym]
		if ob == nil {
			ob = &OrderBook{
				Symbol:    sym,
				BidLevels: btree.NewG[*PriceLevel](2, func(a, b *PriceLevel) bool { return a.Price.LessThan(b.Price) }),
				AskLevels: btree.NewG[*PriceLevel](2, func(a, b *PriceLevel) bool { return a.Price.LessThan(b.Price) }),
			}
			orderbooks[sym] = ob
		}
		ob.Mutex.Lock()
		defer ob.Mutex.Unlock()
		ob.BidLevels.Clear(false)
		ob.AskLevels.Clear(false)
		for _, level := range ob_resp.Bids {
			price := decimal.NewFromProto(*level.Price)
			qty := decimal.NewFromProto(*level.Qty)
			new_level := &PriceLevel{
				Price:  price,
				Qty:    qty,
				Offset: level.Offset,
			}
			ob.BidLevels.ReplaceOrInsert(new_level)
		}
		for _, level := range ob_resp.Asks {
			price := decimal.NewFromProto(*level.Price)
			qty := decimal.NewFromProto(*level.Qty)
			new_level := &PriceLevel{
				Price:  price,
				Qty:    qty,
				Offset: level.Offset,
			}
			ob.AskLevels.ReplaceOrInsert(new_level)
		}
	}
}

func processPsParentOrderExecReport(rep *match_api.PsParentOrderExecReport) {
	log.Debug().Msgf("[*ORDER_VALIDATOR*] processPsParentOrderExecReport: %v", rep)
	if tracer != nil {
		if rep.TraceId != "" {
			traceID, _ := trace.TraceIDFromHex(rep.TraceId)
			sc := trace.NewSpanContext(trace.SpanContextConfig{
				TraceID:    traceID,
				SpanID:     trace.SpanID{}, // Generate new or use valid span ID
				TraceFlags: 0x1,            // Set the sampled flag
				Remote:     false,
			})
			_, span := tracer.Start(trace.ContextWithSpanContext(context.Background(), sc), "processPsParentOrderExecReport")
			defer span.End()
		}
	}
	if rep.PsExecType == match_api.PsExecType_PS_EXEC_TYPE_TRADE {
		processPsParentOrderExecReportTrade(rep)
	} else if rep.PsExecType == match_api.PsExecType_PS_EXEC_TYPE_CANCELED {
		processPsParentOrderExecReportCancel(rep)
	} else if rep.PsExecType == match_api.PsExecType_PS_EXEC_TYPE_REJECTED {
		processPsParentOrderExecReportReject(rep)
	}
}

func processPsParentOrderExecReportTrade(rep *match_api.PsParentOrderExecReport) {
	log.Debug().Msgf("[*ORDER_VALIDATOR*] processPsParentOrderExecReportTrade: %v", rep)
	includePremiumPrice := decimal.Zero
	if rep.IncludePremiumPrice != nil {
		includePremiumPrice = decimal.NewFromProto(*rep.IncludePremiumPrice)
	}
	lastFillQuantity := decimal.Zero
	if rep.LastFillQuantity != nil {
		lastFillQuantity = decimal.NewFromProto(*rep.LastFillQuantity)
	}
	var parentOrderId = rep.OrderId

	pk_enabled := checkPkEnabled(rep.MarketCode)
	var receiveBa *BalanceAccount
	var receiveAmt decimal.Decimal
	var spendBa *BalanceAccount
	var spendAmt decimal.Decimal
	var scenarioReceive, scenarioSpend, scenarioEarmarkFees, scenarioEarmarkTax match_api.Scenario
	var scenarioEarmark match_api.Scenario
	var releaseEarmark decimal.Decimal
	psEarmarkInfo, _ := getPsEarmarkCache(rep.OrderId)
	remainingEarmark := psEarmarkInfo.RemainingEarmark

	if rep.Side == match_api.Side_SIDE_BUY {
		receiveBa = getOrCreateBalanceAccount(rep.BaseBalanceAccountId)
		receiveAmt = lastFillQuantity
		spendBa = balance_accounts[rep.QuoteBalanceAccountId]
		if spendBa == nil {
			log.Error().Msgf("[*PRICE_STREAMING*] processPsParentOrderExecReportTrade quote account is nil for rep.QuoteBalanceAccountId=%s", rep.QuoteBalanceAccountId)
			return
		}
		spendAmt = includePremiumPrice.Mul(lastFillQuantity)
		log.Debug().Msgf("processPsParentOrderExecReportTrade parentOrderId: %s, buy spendAmt: %s, includePremiumPrice : %s, lastFillQuantity : %s, remainingEarmark: %s", parentOrderId, spendAmt, includePremiumPrice, lastFillQuantity, remainingEarmark)
		scenarioReceive = match_api.Scenario_SCENARIO_CONFIRM_BUY_BASE
		scenarioSpend = match_api.Scenario_SCENARIO_CONFIRM_BUY_QUOTE
		scenarioEarmark = match_api.Scenario_SCENARIO_REVERSE_EARMARK_BUY
		scenarioEarmarkFees = match_api.Scenario_SCENARIO_REVERSE_EARMARK_BUY_FEES
		scenarioEarmarkTax = match_api.Scenario_SCENARIO_REVERSE_EARMARK_BUY_TAX

		// Calculate the release earmark and remaining earmark
		if spendAmt.GreaterThan(remainingEarmark) {
			releaseEarmark = remainingEarmark
			remainingEarmark = decimal.Zero
		} else {
			releaseEarmark = spendAmt
			remainingEarmark = remainingEarmark.Sub(spendAmt)
		}
	} else {
		receiveBa = getOrCreateBalanceAccount(rep.QuoteBalanceAccountId)
		receiveAmt = includePremiumPrice.Mul(lastFillQuantity)
		spendBa = balance_accounts[rep.BaseBalanceAccountId]
		if spendBa == nil {
			log.Error().Msgf("[*PRICE_STREAMING*] processPsParentOrderExecReportTrade base account is nil for rep.BaseBalanceAccountId=%s", rep.BaseBalanceAccountId)
			return
		}
		spendAmt = lastFillQuantity
		log.Debug().Msgf("processPsParentOrderExecReportTrade parentOrderId: %s, sell spendAmt: %s, includePremiumPrice : %s, lastFillQuantity : %s, remainingEarmark: %s", parentOrderId, spendAmt, includePremiumPrice, lastFillQuantity, remainingEarmark)
		scenarioReceive = match_api.Scenario_SCENARIO_CONFIRM_SELL_QUOTE
		scenarioSpend = match_api.Scenario_SCENARIO_CONFIRM_SELL_BASE
		scenarioEarmark = match_api.Scenario_SCENARIO_REVERSE_EARMARK_SELL
		scenarioEarmarkFees = match_api.Scenario_SCENARIO_REVERSE_EARMARK_SELL_FEES
		scenarioEarmarkTax = match_api.Scenario_SCENARIO_REVERSE_EARMARK_SELL_TAX

		// Calculate the release earmark and remaining earmark
		if lastFillQuantity.GreaterThan(remainingEarmark) {
			releaseEarmark = remainingEarmark
			remainingEarmark = decimal.Zero
		} else {
			releaseEarmark = lastFillQuantity
			remainingEarmark = remainingEarmark.Sub(lastFillQuantity)
		}
	}

	// Call releaseTradeEarmark to release earmark and update remaining earmark
	quoteBalance, remainingEarmarkFee, remainingEarmarkTax := releaseTradeEarmark(rep, parentOrderId, receiveBa, spendBa, receiveAmt, spendAmt, pk_enabled, scenarioReceive, scenarioSpend, scenarioEarmark, scenarioEarmarkFees, scenarioEarmarkTax, releaseEarmark, remainingEarmark)

	if rep.OrderStatus == match_api.PsParentOrderStatus_PS_PARENT_ORDER_STATUS_CONFIRMED {
		releaseAllEarmarkBalanceAfterConfirmed(rep, parentOrderId, remainingEarmark, remainingEarmarkFee, remainingEarmarkTax, scenarioEarmark, scenarioEarmarkFees, scenarioEarmarkTax, quoteBalance)
	}
	log.Debug().Msgf("processPsParentOrderExecReportTrade done, parentOrderId: %s, remainingEarmark: %v, remainingEarmarkFee: %v, remainingEarmarkTax: %v", parentOrderId, remainingEarmark, remainingEarmarkFee, remainingEarmarkTax)
}

func releaseTradeEarmark(rep *match_api.PsParentOrderExecReport, parentOrderId string, receiveBa, spendBa *BalanceAccount, receiveAmt, spendAmt decimal.Decimal, pk_enabled bool, scenarioReceive, scenarioSpend, scenarioEarmark, scenarioEarmarkFees, scenarioEarmarkTax match_api.Scenario, releaseEarmark, remainingEarmark decimal.Decimal) (quoteBalance *BalanceAccount, remainingEarmarkFee decimal.Decimal, remainingEarmarkTax decimal.Decimal) {

	// Update the remaining earmark
	updateBalances(NewParentExecReport(rep), receiveBa, spendBa, receiveAmt, spendAmt, pk_enabled, scenarioReceive, scenarioSpend)
	// Release earmark logic
	if !releaseEarmark.IsZero() {
		update_balance(rep.OrderId, rep.MarketCode, spendBa, releaseEarmark, match_api.BalanceUpdateType_BALANCE_UPDATE_AVAIL_ONLY, "PS Trade Release Earmark "+rep.LastTradeId, false, match_api.ScenarioType_SCENARIO_TYPE_BALANCE, scenarioEarmark,
			false, "", "", rep.Side, nil, nil, nil, nil, "")
	}
	var releaseEarmarkFee = decimal.NewFromProto(*rep.TradeFeeAmount)
	var releaseEarmarkTax = decimal.NewFromProto(*rep.TradeTaxAmount)

	remainingEarmarkFee = decimal.NewFromProto(*rep.RemainingEarmarkFee)
	remainingEarmarkTax = decimal.NewFromProto(*rep.RemainingEarmarkTax)
	log.Debug().Msgf("Call releaseTradeEarmark, parentOrderId: %s, releaseEarmarkFee: %v, releaseEarmarkTax: %v, remainingEarmarkFee: %v, remainingEarmarkTax: %v", parentOrderId, releaseEarmarkFee, releaseEarmarkTax, remainingEarmarkFee, remainingEarmarkTax)

	// Update the remaining earmark
	psEarmarkCache[rep.OrderId] = PsEarmarkInfo{RemainingEarmark: remainingEarmark, RemainingEarmarkFee: remainingEarmarkFee, RemainingEarmarkTax: remainingEarmarkTax}

	quoteBalance = balance_accounts[rep.QuoteBalanceAccountId]
	// Release fee earmark
	if !releaseEarmarkFee.IsZero() {
		// Update the current balance
		update_balance_neg(rep.OrderId, rep.MarketCode, quoteBalance, releaseEarmarkFee, match_api.BalanceUpdateType_BALANCE_UPDATE_TOTAL_AVAIL, "PS Trade Fee "+rep.LastTradeId, false, match_api.ScenarioType_SCENARIO_TYPE_FEES, scenarioEarmarkFees,
			false, "", "", rep.Side, nil, nil, nil, nil, "")
		// Update the earmark balance
		update_balance(rep.OrderId, rep.MarketCode, quoteBalance, releaseEarmarkFee, match_api.BalanceUpdateType_BALANCE_UPDATE_AVAIL_ONLY, "PS Trade Release Fee Earmark "+rep.LastTradeId, false, match_api.ScenarioType_SCENARIO_TYPE_FEES, scenarioEarmarkFees,
			false, "", "", rep.Side, nil, nil, nil, nil, "")
	}

	// Release tax earmark
	if !releaseEarmarkTax.IsZero() {
		// Update the current balance
		update_balance_neg(rep.OrderId, rep.MarketCode, quoteBalance, releaseEarmarkTax, match_api.BalanceUpdateType_BALANCE_UPDATE_TOTAL_AVAIL, "PS Trade Tax "+rep.LastTradeId, false, match_api.ScenarioType_SCENARIO_TYPE_TAX, scenarioEarmarkTax,
			false, "", "", rep.Side, nil, nil, nil, nil, "")
		// Update the earmark balance
		update_balance(rep.OrderId, rep.MarketCode, quoteBalance, releaseEarmarkTax, match_api.BalanceUpdateType_BALANCE_UPDATE_AVAIL_ONLY, "PS Trade Release Tax Earmark "+rep.LastTradeId, false, match_api.ScenarioType_SCENARIO_TYPE_TAX, scenarioEarmarkTax,
			false, "", "", rep.Side, nil, nil, nil, nil, "")
	}
	return quoteBalance, remainingEarmarkFee, remainingEarmarkTax
}

func releaseAllEarmarkBalanceAfterConfirmed(rep *match_api.PsParentOrderExecReport, parentOrderId string, remainingEarmark, remainingEarmarkFee, remainingEarmarkTax decimal.Decimal, scenarioEarmark match_api.Scenario, scenarioEarmarkFees match_api.Scenario, scenarioEarmarkTax match_api.Scenario, quoteBalance *BalanceAccount) {
	log.Debug().Msgf("releaseAllEarmarkBalanceAfterConfirmed Order confirmed, parentOrderId: %s, remainingEarmark: %v, remainingEarmarkFee: %v, remainingEarmarkTax: %v", parentOrderId, remainingEarmark, remainingEarmarkFee, remainingEarmarkTax)
	earmarkAmount := decimal.NewFromProto(*rep.EarmarkAmt)
	earmarkAmountString := earmarkAmount.String()
	cumFillQuantity := decimal.NewFromProto(*rep.CumulativeFillQuantity)
	// Use trade volume weighted average price (include premium)
	if rep.ExecIncludePremiumAveragePrice == nil {
		log.Warn().Msgf("Old trade message, ExecIncludePremiumAveragePrice is nil, direct return")
		return
	}
	var execIncludePremiumAveragePrice = decimal.NewFromProto(*rep.ExecIncludePremiumAveragePrice)
	cumFillAmount := cumFillQuantity.Mul(execIncludePremiumAveragePrice)

	var earmarkBalance *BalanceAccount
	if rep.Side == match_api.Side_SIDE_BUY {
		log.Debug().Msgf("releaseAllEarmarkBalanceAfterConfirmed parentOrderId: %s, Buy trade confirmed, remain_earmark: %v", parentOrderId, remainingEarmark)
		earmarkBalance = balance_accounts[rep.QuoteBalanceAccountId]
		if earmarkBalance == nil {
			panic(QUOTE_ACCOUNT_IS_NIL_ERRORR)
		}
		scenarioEarmark = match_api.Scenario_SCENARIO_RETURN_EARMARK_BUY
	} else if rep.Side == match_api.Side_SIDE_SELL {
		log.Debug().Msgf("releaseAllEarmarkBalanceAfterConfirmed parentOrderId: %s, Sell trade confirmed, remain_earmark: %v", parentOrderId, remainingEarmark)
		earmarkBalance = balance_accounts[rep.BaseBalanceAccountId]
		if earmarkBalance == nil {
			panic(BASE_ACCOUNT_IS_NIL_ERRORR)
		}
		scenarioEarmark = match_api.Scenario_SCENARIO_RETURN_EARMARK_SELL
	}

	log.Debug().Msgf("releaseAllEarmarkBalanceAfterConfirmed Trade confirmed parentOrderId: %s, earmarkAmount: %v, earmarkAmountString: %v, cumFillAmount: %v, cumFillQuantity: %v, remain_earmark: %v, remain_earmark_amt: %v", parentOrderId, earmarkAmount, earmarkAmountString, cumFillAmount, cumFillQuantity, execIncludePremiumAveragePrice, remainingEarmark)

	if !remainingEarmark.IsZero() {
		// Update the remainingEarmark for trading account
		update_balance(rep.OrderId, rep.MarketCode, earmarkBalance, remainingEarmark, match_api.BalanceUpdateType_BALANCE_UPDATE_AVAIL_ONLY, "PS Release Earmark "+rep.LastTradeId, false, match_api.ScenarioType_SCENARIO_TYPE_BALANCE, scenarioEarmark,
			false, "", "", rep.Side, nil, nil, nil, nil, "")
	}

	// Clear the remain_earmark for ps parent order
	// Release remain fee earmark
	if !remainingEarmarkFee.IsZero() {
		update_balance(rep.OrderId, rep.MarketCode, quoteBalance, remainingEarmarkFee, match_api.BalanceUpdateType_BALANCE_UPDATE_AVAIL_ONLY, "PS Release Fee Earmark "+rep.LastTradeId, false, match_api.ScenarioType_SCENARIO_TYPE_FEES, scenarioEarmarkFees,
			false, "", "", rep.Side, nil, nil, nil, nil, "")
	}

	// Release remain tax earmark
	if !remainingEarmarkTax.IsZero() {
		update_balance(rep.OrderId, rep.MarketCode, quoteBalance, remainingEarmarkTax, match_api.BalanceUpdateType_BALANCE_UPDATE_AVAIL_ONLY, "PS Release Tax Earmark "+rep.LastTradeId, false, match_api.ScenarioType_SCENARIO_TYPE_TAX, scenarioEarmarkTax,
			false, "", "", rep.Side, nil, nil, nil, nil, "")
	}

	// Clear the PsEarmarkCache for ps parent order
	deletePsEarmarkCache(rep.OrderId)

	// Handle dealer release earmark
	psDealerEarmarkInfo, exists := getPsEarmarkCache(rep.OrderId + SUFFIX_DEALER)
	if exists {
		dealerRemainingEarmark := psDealerEarmarkInfo.RemainingEarmark
		operatorEarmarkBalanceAccountId := psDealerEarmarkInfo.OperatorEarmarkBalanceAccountId

		operatorEarmarkBalance := balance_accounts[operatorEarmarkBalanceAccountId]
		if !remainingEarmark.IsZero() {
			update_balance(rep.OrderId, rep.MarketCode, operatorEarmarkBalance, dealerRemainingEarmark, match_api.BalanceUpdateType_BALANCE_UPDATE_AVAIL_ONLY, "PS Operator Release Earmark "+rep.OrderId, false, match_api.ScenarioType_SCENARIO_TYPE_BALANCE, scenarioEarmark,
				false, "", "", rep.Side, nil, nil, nil, nil, "")
		}
		// Clear the PsEarmarkCache for dealer order
		deletePsEarmarkCache(rep.OrderId + SUFFIX_DEALER)
	}
}

func getOrCreateBalanceAccount(accountId string) *BalanceAccount {
	if ba, ok := balance_accounts[accountId]; ok {
		return ba
	}

	splits := strings.Split(accountId, ":")
	newBA := &BalanceAccount{
		BalanceAccountId: accountId,
		HoldingAccountId: splits[1],
		AssetSymbol:      splits[2],
		AvailBalance:     decimal.Zero,
		TotalBalance:     decimal.Zero,
		PendingIN:        decimal.Zero,
		PendingOUT:       decimal.Zero,
	}
	balance_accounts[accountId] = newBA
	return newBA
}

func updateBalances(rep *PsOrderExecReport, receiveBA, spendBA *BalanceAccount,
	receiveAmt, spendAmt decimal.Decimal, pk_enabled bool, scenarios ...match_api.Scenario) {
	tradeID := rep.PsTradeId
	update_balance(rep.OrderId, rep.MarketCode, receiveBA, receiveAmt,
		match_api.BalanceUpdateType_BALANCE_UPDATE_TOTAL_AVAIL,
		"PS Trade "+tradeID, false,
		match_api.ScenarioType_SCENARIO_TYPE_BALANCE, scenarios[0],
		pk_enabled, rep.ServiceAccountId, rep.Symbol, rep.Side,
		rep.LastFillQuantity, rep.LastFillPrice,
		rep.CumulativeFillAmount, utils.ZeroUdec, "")

	update_balance_neg(rep.OrderId, rep.MarketCode, spendBA, spendAmt,
		match_api.BalanceUpdateType_BALANCE_UPDATE_TOTAL_AVAIL,
		"PS Trade "+tradeID, false,
		match_api.ScenarioType_SCENARIO_TYPE_BALANCE, scenarios[1],
		false, "", "", rep.Side, nil, nil, nil, nil, "")
}

func NewChildExecReport(rep *match_api.PsChildOrderExecReport, marketCode string) *PsOrderExecReport {
	return &PsOrderExecReport{
		OrderId:              rep.OrderId,
		MarketCode:           marketCode,
		Side:                 rep.Side,
		Symbol:               rep.Symbol,
		ServiceAccountId:     rep.ServiceAccountId,
		LastFillQuantity:     rep.LastFillQuantity,
		LastFillPrice:        rep.LastFillPrice,
		PsTradeId:            rep.PsTradeId,
		CumulativeFillAmount: rep.CumulativeFillAmount,
	}
}

func NewParentExecReport(rep *match_api.PsParentOrderExecReport) *PsOrderExecReport {
	return &PsOrderExecReport{
		OrderId:          rep.OrderId,
		MarketCode:       rep.MarketCode,
		Side:             rep.Side,
		Symbol:           rep.Symbol,
		ServiceAccountId: rep.ServiceAccountId,
		LastFillQuantity: rep.LastFillQuantity,
		LastFillPrice:    rep.LastFillPrice,
		PsTradeId:        rep.LastTradeId,
	}
}

func calculateEarmark(spendAmt, remainingEarmark decimal.Decimal) (release, remain decimal.Decimal) {
	if spendAmt.GreaterThan(remainingEarmark) {
		return remainingEarmark, decimal.Zero
	}
	return spendAmt, remainingEarmark.Sub(spendAmt)
}

func processPsParentOrderExecReportReject(rep *match_api.PsParentOrderExecReport) {
	log.Debug().Msgf("[*ORDER_VALIDATOR*] processPsParentOrderExecReportReject: %v", rep)
	var scenarioEarmark, scenarioEarmarkFees, scenarioEarmarkTax match_api.Scenario
	var earmarkBalance *BalanceAccount
	if rep.GetEarmarkAmt() == nil {
		log.Error().Msgf("earmark amount is nil")
		return
	}
	if rep.Side == match_api.Side_SIDE_BUY {
		scenarioEarmark = match_api.Scenario_SCENARIO_REJECT_ORDER_BUY
		scenarioEarmarkFees = match_api.Scenario_SCENARIO_REJECT_ORDER_BUY_FEES
		scenarioEarmarkTax = match_api.Scenario_SCENARIO_REJECT_ORDER_BUY_TAX
		earmarkBalance = balance_accounts[rep.QuoteBalanceAccountId]
		if earmarkBalance == nil {
			log.Error().Msgf(QUOTE_ACCOUNT_IS_NIL_ERRORR)
			return
		}
	} else if rep.Side == match_api.Side_SIDE_SELL {
		scenarioEarmark = match_api.Scenario_SCENARIO_REJECT_ORDER_SELL
		scenarioEarmarkFees = match_api.Scenario_SCENARIO_REJECT_ORDER_SELL_FEES
		scenarioEarmarkTax = match_api.Scenario_SCENARIO_REJECT_ORDER_SELL_TAX
		earmarkBalance = balance_accounts[rep.BaseBalanceAccountId]
		if earmarkBalance == nil {
			log.Error().Msgf(BASE_ACCOUNT_IS_NIL_ERRORR)
			return
		}
	}
	psEarmarkInfo, _ := getPsEarmarkCache(rep.OrderId)
	remainingEarmark := psEarmarkInfo.RemainingEarmark
	remainingEarmarkFee := psEarmarkInfo.RemainingEarmarkFee
	remainingEarmarkTax := psEarmarkInfo.RemainingEarmarkTax
	// Fee and tax earmark account
	quoteBalance := balance_accounts[rep.QuoteBalanceAccountId]

	// Release remain earmark
	if !remainingEarmark.IsZero() {
		update_balance(rep.OrderId, rep.MarketCode, earmarkBalance, remainingEarmark, match_api.BalanceUpdateType_BALANCE_UPDATE_AVAIL_ONLY, "PS Reject Order Release Earmark "+rep.OrderId, false, match_api.ScenarioType_SCENARIO_TYPE_BALANCE, scenarioEarmark,
			false, "", "", rep.Side, nil, nil, nil, nil, "")
	}
	// Release remain fee earmark
	if !remainingEarmarkFee.IsZero() {
		update_balance(rep.OrderId, rep.MarketCode, quoteBalance, remainingEarmarkFee, match_api.BalanceUpdateType_BALANCE_UPDATE_AVAIL_ONLY, "PS Reject Order Release Fee Earmark "+rep.OrderId, false, match_api.ScenarioType_SCENARIO_TYPE_FEES, scenarioEarmarkFees,
			false, "", "", rep.Side, nil, nil, nil, nil, "")
	}

	// Release remain tax earmark
	if !remainingEarmarkTax.IsZero() {
		update_balance(rep.OrderId, rep.MarketCode, quoteBalance, remainingEarmarkTax, match_api.BalanceUpdateType_BALANCE_UPDATE_AVAIL_ONLY, "PS Reject Order Release Tax Earmark "+rep.OrderId, false, match_api.ScenarioType_SCENARIO_TYPE_TAX, scenarioEarmarkTax,
			false, "", "", rep.Side, nil, nil, nil, nil, "")
	}

	// Clear the PsEarmarkCache for ps parent order
	deletePsEarmarkCache(rep.OrderId)
}

func processPsParentOrderExecReportCancel(rep *match_api.PsParentOrderExecReport) {
	log.Debug().Msgf("[*ORDER_VALIDATOR*] processPsParentOrderExecReportCancel: %v", rep)
	var scenarioEarmark, scenarioEarmarkFees, scenarioEarmarkTax match_api.Scenario
	var earmarkBalance *BalanceAccount
	if rep.GetEarmarkAmt() == nil || rep.GetCumulativeFillQuantity() == nil || rep.GetCumulativeFillAmount() == nil {
		log.Error().Msgf("earmark amount, cumulative fill quantity or cumulative fill amount is nil")
		return
	}
	earmarkAmount := decimal.NewFromProto(*rep.EarmarkAmt)
	earmarkAmountString := earmarkAmount.String()
	if rep.ExecIncludePremiumAveragePrice == nil {
		log.Warn().Msgf("Old trade message, ExecIncludePremiumAveragePrice is nil, direct return")
		return
	}
	var execIncludePremiumAveragePrice = decimal.NewFromProto(*rep.ExecIncludePremiumAveragePrice)
	cumFillQuantity := decimal.NewFromProto(*rep.CumulativeFillQuantity)
	cumFillAmount := cumFillQuantity.Mul(execIncludePremiumAveragePrice)

	psEarmarkInfo, _ := getPsEarmarkCache(rep.OrderId)
	remainingEarmark := psEarmarkInfo.RemainingEarmark
	remainingEarmarkFee := psEarmarkInfo.RemainingEarmarkFee
	remainingEarmarkTax := psEarmarkInfo.RemainingEarmarkTax
	// Fee and tax earmark account
	quoteBalance := balance_accounts[rep.QuoteBalanceAccountId]

	if rep.Side == match_api.Side_SIDE_BUY {
		scenarioEarmark = match_api.Scenario_SCENARIO_CANCEL_ORDER_BUY
		scenarioEarmarkFees = match_api.Scenario_SCENARIO_CANCEL_ORDER_BUY_FEES
		scenarioEarmarkTax = match_api.Scenario_SCENARIO_CANCEL_ORDER_BUY_TAX
		cumFillAmountString := cumFillAmount.String()
		log.Debug().Msgf("Buy canceled, earmarkAmount: %v, earmarkAmountString: %v, remain_earmark: %v, cumFillAmount: %v, cumFillAmountString: %v", earmarkAmount, earmarkAmountString, remainingEarmark, cumFillAmount, cumFillAmountString)
		earmarkBalance = balance_accounts[rep.QuoteBalanceAccountId]
		if earmarkBalance == nil {
			log.Error().Msgf(QUOTE_ACCOUNT_IS_NIL_ERRORR)
			return
		}
	} else if rep.Side == match_api.Side_SIDE_SELL {
		scenarioEarmark = match_api.Scenario_SCENARIO_CANCEL_ORDER_SELL
		scenarioEarmarkFees = match_api.Scenario_SCENARIO_CANCEL_ORDER_SELL_FEES
		scenarioEarmarkTax = match_api.Scenario_SCENARIO_CANCEL_ORDER_SELL_TAX
		cumFillQuantityString := cumFillQuantity.String()
		log.Debug().Msgf("Sell canceled, earmarkAmount: %v, earmarkAmountString: %v, remain_earmark: %v, cumFillQuantity: %v, cumFillQuantityString: %v", earmarkAmount, earmarkAmountString, remainingEarmark, cumFillQuantity, cumFillQuantityString)
		earmarkBalance = balance_accounts[rep.BaseBalanceAccountId]
		if earmarkBalance == nil {
			log.Error().Msgf(BASE_ACCOUNT_IS_NIL_ERRORR)
			return
		}
	}

	log.Debug().Msgf("Order canceled, earmarkAmount: %v, earmarkAmountString: %v, cumFillAmount: %v, cumFillQuantity: %v, execIncludePremiumAveragePrice: %v, remain_earmark: %v", earmarkAmount, earmarkAmountString, cumFillAmount, cumFillQuantity, execIncludePremiumAveragePrice, remainingEarmark)
	if !remainingEarmark.IsZero() {
		update_balance(rep.OrderId, rep.MarketCode, earmarkBalance, remainingEarmark, match_api.BalanceUpdateType_BALANCE_UPDATE_AVAIL_ONLY, "PS Cancel Order Release Earmark"+rep.OrderId, false, match_api.ScenarioType_SCENARIO_TYPE_BALANCE, scenarioEarmark,
			false, "", "", rep.Side, nil, nil, nil, nil, "")
	}

	// Release remain fee earmark
	if !remainingEarmarkFee.IsZero() {
		update_balance(rep.OrderId, rep.MarketCode, quoteBalance, remainingEarmarkFee, match_api.BalanceUpdateType_BALANCE_UPDATE_AVAIL_ONLY, "PS Cancel Order Release Fee Earmark"+rep.OrderId, false, match_api.ScenarioType_SCENARIO_TYPE_FEES, scenarioEarmarkFees,
			false, "", "", rep.Side, nil, nil, nil, nil, "")
	}

	// Release remain tax earmark
	if !remainingEarmarkTax.IsZero() {
		update_balance(rep.OrderId, rep.MarketCode, quoteBalance, remainingEarmarkTax, match_api.BalanceUpdateType_BALANCE_UPDATE_AVAIL_ONLY, "PS Cancel Order Release Tax Earmark"+rep.OrderId, false, match_api.ScenarioType_SCENARIO_TYPE_TAX, scenarioEarmarkTax,
			false, "", "", rep.Side, nil, nil, nil, nil, "")
	}

	// Clear the PsEarmarkCache for ps parent order
	deletePsEarmarkCache(rep.OrderId)
}

func processPsChildOrderExecReport(childOrder *match_api.PsChildOrderExecReport) {
	log.Debug().Msgf("[*ORDER_VALIDATOR*] processPsChildOrderExecReport: %v", childOrder)
	marketCode := strings.Split(childOrder.Symbol, ":")[0]
	var holdingAccountNumber string

	for _, v := range tradeVenueAccounts {
		market := markets[v.MarketCode]
		if v.EntityType == "MARKET" && market.MarketModel == "dealer" && market.Code == marketCode {
			holdingAccountNumber = v.AhaNumber
			break
		}
	}
	if holdingAccountNumber == "" {
		log.Warn().Msgf("holding account number not found for market_code=%s", marketCode)
		return
	}
	if childOrder.PsExecType == match_api.PsExecType_PS_EXEC_TYPE_TRADE {
		processPsChildOrderExecReportTrade(childOrder, marketCode, holdingAccountNumber)
	} else if childOrder.PsExecType == match_api.PsExecType_PS_EXEC_TYPE_CANCELED {
		processPsChildOrderExecReportCancel(childOrder, marketCode, holdingAccountNumber)
	} else if childOrder.PsExecType == match_api.PsExecType_PS_EXEC_TYPE_REJECTED {
		processPsChildOrderExecReportReject(childOrder, marketCode, holdingAccountNumber)
	}
}

func processPsChildOrderExecReportTrade(childOrder *match_api.PsChildOrderExecReport, marketCode string, holdingAccountNumber string) {
	log.Debug().Msgf("[*ORDER_VALIDATOR*] processPsChildOrderExecReportTrade: %v", childOrder)
	checkChildOrderExecReport(childOrder)
	var lastFillQty = decimal.NewFromProto(*childOrder.LastFillQuantity)
	lastFillPrice := decimal.NewFromProto(*childOrder.LastFillPrice)
	pk_enabled := checkPkEnabled(marketCode)
	baseBalanceAccountId, quoteBalanceAccountId, err := getBalanceAccountByMarketCodeAndAhaNumber(childOrder.Symbol, marketCode, holdingAccountNumber)
	if err != nil {
		return
	}
	var receiveBa *BalanceAccount
	var receiveAmt decimal.Decimal
	var spendBa *BalanceAccount
	var spendAmt decimal.Decimal
	var dealerReleaseEarmark decimal.Decimal
	var scenarioReceive, scenarioSpend, scenarioEarmark match_api.Scenario

	parentOrderId := childOrder.ParentOrderId
	dealerOrderIdKey := parentOrderId + SUFFIX_DEALER
	psDealerEarmarkInfo, _ := getPsEarmarkCache(dealerOrderIdKey)
	dealerRemainingEarmark := psDealerEarmarkInfo.RemainingEarmark
	log.Debug().Msgf("processPsChildOrderExecReportTrade get dealerOrderIdKey: %v, dealerRemainingEarmark: %v, psDealerEarmarkInfo: %v", dealerOrderIdKey, dealerRemainingEarmark, psDealerEarmarkInfo)
	if childOrder.Side == match_api.Side_SIDE_BUY {
		receiveBa = getOrCreateBalanceAccount(baseBalanceAccountId)
		receiveAmt = lastFillQty
		spendBa = balance_accounts[quoteBalanceAccountId]
		if spendBa == nil {
			log.Error().Msgf("[*PRICE_STREAMING*] processPsChildOrderExecReportTrade quote account is nil for rep.QuoteBalanceAccountId=%s", quoteBalanceAccountId)
			return
		}
		spendAmt = lastFillPrice.Mul(lastFillQty)
		log.Debug().Msgf("spend_amt : %s, last_fill_qty : %s", spendAmt, lastFillQty)
		scenarioReceive = match_api.Scenario_SCENARIO_CONFIRM_BUY_BASE
		scenarioSpend = match_api.Scenario_SCENARIO_CONFIRM_BUY_QUOTE
		scenarioEarmark = match_api.Scenario_SCENARIO_REVERSE_EARMARK_BUY

		// Calculate the release earmark and remaining earmark
		if spendAmt.GreaterThan(dealerRemainingEarmark) {
			dealerReleaseEarmark = dealerRemainingEarmark
			dealerRemainingEarmark = decimal.Zero
		} else {
			dealerReleaseEarmark = spendAmt
			dealerRemainingEarmark = dealerRemainingEarmark.Sub(spendAmt)
		}
	} else {
		receiveBa = getOrCreateBalanceAccount(quoteBalanceAccountId)
		receiveAmt = lastFillPrice.Mul(lastFillQty)
		spendBa = balance_accounts[baseBalanceAccountId]
		if spendBa == nil {
			log.Error().Msgf("[*PRICE_STREAMING*] processPsChildOrderExecReportTrade base account is nil for rep.BaseBalanceAccountId=%s", baseBalanceAccountId)
			return
		}
		spendAmt = lastFillQty
		log.Debug().Msgf("spend_amt : %s, last_fill_qty : %s", spendAmt, lastFillQty)
		scenarioReceive = match_api.Scenario_SCENARIO_CONFIRM_SELL_QUOTE
		scenarioSpend = match_api.Scenario_SCENARIO_CONFIRM_SELL_BASE
		scenarioEarmark = match_api.Scenario_SCENARIO_REVERSE_EARMARK_SELL

		// Calculate the release earmark and remaining earmark
		if lastFillQty.GreaterThan(dealerRemainingEarmark) {
			dealerReleaseEarmark = dealerRemainingEarmark
			dealerRemainingEarmark = decimal.Zero
		} else {
			dealerReleaseEarmark = lastFillQty
			dealerRemainingEarmark = dealerRemainingEarmark.Sub(lastFillQty)
		}
	}
	// Update the remaining earmark
	psEarmarkCache[dealerOrderIdKey] = PsEarmarkInfo{RemainingEarmark: dealerRemainingEarmark}
	log.Debug().Msgf("processPsChildOrderExecReportTrade get dealerOrderIdKey: %v, psDealerEarmarkInfo: %v", dealerOrderIdKey, psEarmarkCache[dealerOrderIdKey])

	psEarmarkInfo, _ := getPsEarmarkCache(parentOrderId)
	remainingEarmark := psEarmarkInfo.RemainingEarmark
	remainingEarmarkFee := psEarmarkInfo.RemainingEarmarkFee
	remainingEarmarkTax := psEarmarkInfo.RemainingEarmarkTax
	operatorQuoteBalanceAccountId := psEarmarkInfo.OperatorEarmarkBalanceAccountId

	psEarmarkCache[parentOrderId] = PsEarmarkInfo{RemainingEarmark: remainingEarmark, RemainingEarmarkFee: remainingEarmarkFee, RemainingEarmarkTax: remainingEarmarkTax, OperatorEarmarkBalanceAccountId: operatorQuoteBalanceAccountId}

	log.Debug().Msgf("processPsChildOrderExecReportTrade get parentOrderId: %v, psEarmarkInfo: %v, dealerReleaseEarmark: %v, dealerRemainingEarmark: %v", parentOrderId, psEarmarkCache[parentOrderId], dealerReleaseEarmark, dealerRemainingEarmark)

	childOrder.PsTradeId = childOrder.PsTradeId + "-01"
	// Update the remaining earmark
	updateBalances(NewChildExecReport(childOrder, marketCode), receiveBa, spendBa, receiveAmt, spendAmt, pk_enabled, scenarioReceive, scenarioSpend)
	// Release earmark logic
	if !dealerReleaseEarmark.IsZero() {
		update_balance(childOrder.ParentOrderId, marketCode, spendBa, dealerReleaseEarmark, match_api.BalanceUpdateType_BALANCE_UPDATE_AVAIL_ONLY, "PS Operator Trade "+childOrder.PsTradeId, false, match_api.ScenarioType_SCENARIO_TYPE_BALANCE, scenarioEarmark,
			false, "", "", childOrder.Side, nil, nil, nil, nil, "")
	}
}

func checkChildOrderExecReport(childOrder *match_api.PsChildOrderExecReport) {
	if childOrder.MarketModel == "broker" {
		log.Trace().Msgf("[*ORDER_VALIDATOR*] ignore broker child order trade: %v", childOrder)
		return
	}
	if childOrder.GetLastFillPrice() == nil || childOrder.GetLastFillQuantity() == nil {
		log.Error().Msg("invalid fill data")
	}
}

func checkPkEnabled(marketCode string) bool {
	pk_enabled := false
	if mkt, ok := markets[marketCode]; ok {
		pk_enabled = mkt.PositionKeeping.Bool
		if mkt.EnableExtSettlementConfirm {
			// TODO: implement settlement confirm
		}
	}
	return pk_enabled
}
func processPsChildOrderExecReportCancel(childOrder *match_api.PsChildOrderExecReport, marketCode string, holdingAccountNumber string) {
	log.Debug().Msgf("[*ORDER_VALIDATOR*] processPsChildOrderExecReportCancel: %v", childOrder)
	var scenarioEarmark match_api.Scenario
	var earmarkBalance *BalanceAccount
	mkt := markets[marketCode]
	if mkt == nil {
		log.Error().Msgf("market not found for market_code=%s", marketCode)
		return
	}
	baseBalanceAccountId, quoteBalanceAccountId, err := getBalanceAccountByMarketCodeAndAhaNumber(childOrder.Symbol, marketCode, holdingAccountNumber)
	if err != nil {
		return
	}
	if childOrder.Side == match_api.Side_SIDE_BUY {
		scenarioEarmark = match_api.Scenario_SCENARIO_CANCEL_ORDER_BUY
		earmarkBalance = balance_accounts[quoteBalanceAccountId]
		if earmarkBalance == nil {
			log.Error().Msgf(QUOTE_ACCOUNT_IS_NIL_ERRORR)
			return
		}
	} else if childOrder.Side == match_api.Side_SIDE_SELL {
		scenarioEarmark = match_api.Scenario_SCENARIO_CANCEL_ORDER_SELL
		earmarkBalance = balance_accounts[baseBalanceAccountId]
		if earmarkBalance == nil {
			log.Error().Msgf(BASE_ACCOUNT_IS_NIL_ERRORR)
			return
		}
	}
	dealerOrderIdKey := childOrder.ParentOrderId + SUFFIX_DEALER

	psEarmarkInfo, _ := getPsEarmarkCache(dealerOrderIdKey)
	remainingEarmark := psEarmarkInfo.RemainingEarmark
	log.Debug().Msgf("processPsChildOrderExecReportCancel get dealerOrderIdKey: %v, psEarmarkInfo: %v", dealerOrderIdKey, psEarmarkCache[dealerOrderIdKey])

	// Release earmark logic
	if !remainingEarmark.IsZero() {
		update_balance(childOrder.ParentOrderId, marketCode, earmarkBalance, remainingEarmark, match_api.BalanceUpdateType_BALANCE_UPDATE_AVAIL_ONLY, "PS Operator Cancel Order Release Earmark"+childOrder.ParentOrderId, false, match_api.ScenarioType_SCENARIO_TYPE_BALANCE, scenarioEarmark,
			false, "", "", childOrder.Side, nil, nil, nil, nil, "")
	}

	// Clear the remain_earmark for ps parent order
	deletePsEarmarkCache(dealerOrderIdKey)
}

func processPsChildOrderExecReportReject(childOrder *match_api.PsChildOrderExecReport, marketCode string, holdingAccountNumber string) {
	log.Debug().Msgf("[*ORDER_VALIDATOR*] processPsChildOrderExecReportReject: %v", childOrder)
	var scenarioEarmark match_api.Scenario
	var earmarkBalance *BalanceAccount
	baseBalanceAccountId, quoteBalanceAccountId, err := getBalanceAccountByMarketCodeAndAhaNumber(childOrder.Symbol, marketCode, holdingAccountNumber)
	if err != nil {
		return
	}
	if childOrder.Side == match_api.Side_SIDE_BUY {
		scenarioEarmark = match_api.Scenario_SCENARIO_REJECT_ORDER_BUY
		earmarkBalance = balance_accounts[quoteBalanceAccountId]
		if earmarkBalance == nil {
			log.Error().Msgf(QUOTE_ACCOUNT_IS_NIL_ERRORR)
			return
		}
	} else if childOrder.Side == match_api.Side_SIDE_SELL {
		scenarioEarmark = match_api.Scenario_SCENARIO_REJECT_ORDER_SELL
		earmarkBalance = balance_accounts[baseBalanceAccountId]
		if earmarkBalance == nil {
			log.Error().Msgf(BASE_ACCOUNT_IS_NIL_ERRORR)
			return
		}
	}
	dealerOrderIdKey := childOrder.ParentOrderId + SUFFIX_DEALER
	psEarmarkInfo, _ := getPsEarmarkCache(dealerOrderIdKey)
	remainingEarmark := psEarmarkInfo.RemainingEarmark
	log.Debug().Msgf("processPsChildOrderExecReportReject get dealerOrderIdKey: %v, psEarmarkInfo: %v", dealerOrderIdKey, psEarmarkCache[dealerOrderIdKey])

	if !remainingEarmark.IsZero() {
		update_balance(childOrder.ParentOrderId, marketCode, earmarkBalance, remainingEarmark, match_api.BalanceUpdateType_BALANCE_UPDATE_AVAIL_ONLY, "PS Operator Reject Order Release Earmark"+childOrder.ParentOrderId, false, match_api.ScenarioType_SCENARIO_TYPE_BALANCE, scenarioEarmark,
			false, "", "", childOrder.Side, nil, nil, nil, nil, "")
	}

	// Clear the remain_earmark for ps parent order
	deletePsEarmarkCache(dealerOrderIdKey)
}

package main

import (
	"me_two/pkg/decimal"
	"me_two/pkg/match_api"
	"me_two/pkg/utils"

	"os/exec"
	"time"

	"github.com/rs/zerolog/log"
	"github.com/spf13/viper"

	//"github.com/google/btree"
	"me_two/pkg/btree"

	"pgregory.net/rand"

	"github.com/lirm/aeron-go/aeron"
	"github.com/lirm/aeron-go/aeron/atomic"

	"github.com/lirm/aeron-go/aeron/idlestrategy"
	//"github.com/lirm/aeron-go/aeron/idlestrategy"
	"strings"
	"sync"

	"github.com/lirm/aeron-go/aeron/logbuffer"
	"github.com/lirm/aeron-go/cluster"
	"github.com/lirm/aeron-go/cluster/codecs"
	"github.com/quickfixgo/enum"
	"go.uber.org/zap/zapcore"
	"os"
)

type MatchService struct {
	cluster cluster.Cluster
}

type MatchSession struct {
	Source             string
	OrderValidatorName string
}

var match_sessions = make(map[int64]*MatchSession)

var aeron_match_service *MatchService

var match_in_sequences = make(map[string]uint64)

var svc_match_read_buf = make([]byte, 30000)
var svc_match_send_buf = make([]byte, 30000)
var AeronSvcMatchConnected = false
var svc_match_role = cluster.Follower

func restoreOb(snap *match_api.MatchSnapshot) {
	for _, ob_snap := range snap.Orderbooks {
		sym := ob_snap.Symbol
		ob := &OrderBook{}
		if orderbooks[sym] != nil {
			ob = orderbooks[sym]
		}
		ob.Symbol = sym
		ob.bid_levels = btree.NewG[*PriceLevel](2, func(a, b *PriceLevel) bool { return a.price.LessThan(b.price) })
		ob.ask_levels = btree.NewG[*PriceLevel](2, func(a, b *PriceLevel) bool { return a.price.LessThan(b.price) })
		ob.cl_open_orders = make(map[string]*Order)
		ob.Rand = rand.New(uint64(GetClusterTimeUnixNano()))
		ob.LastTradePrice = decimal.NewFromProto(*ob_snap.LastTradePrice)
		ob.LastTradeQty = decimal.NewFromProto(*ob_snap.LastTradeQty)
		ob.LastTradeTime = ob_snap.LastTradeTime
		ob.next_price_offset = ob_snap.NextPriceOffset
		market_code := strings.Split(sym, ":")
		market := markets[market_code[0]]
		if market == nil {
			log.Fatal().Msgf("Market code not found %s", market_code[0])
		}
		ob.Market = market

		var err error
		for _, o_snap := range ob_snap.OpenOrders {
			//t := uint64(time.Now().UnixNano())
			openQty := decimal.NewFromProto(*o_snap.OpenQty)
			if openQty.IsZero() {
				// to handle order that's fully matched but not cleared from orderbook snapshot
				log.Warn().Msgf("OpenQty is zero for order %s, cl_ord_id %s, symbol %s", o_snap.OrderId, o_snap.ClOrdId, o_snap.Symbol)
				continue // skip orders with zero open quantity

			}
			order := new_order()
			order.BondPricePct = o_snap.BondPricePct
			order.Orderbook = ob
			order.OrderId = o_snap.OrderId
			order.ClOrdId = o_snap.ClOrdId
			order.Symbol = o_snap.Symbol
			order.Price = decimal.NewFromProto(*o_snap.Price)
			order.Qty = decimal.NewFromProto(*o_snap.Qty)
			order.HoldingAccountId = o_snap.HoldingAccountId
			order.ServiceAccountId = o_snap.ServiceAccountId
			order.BaseBalanceAccountId = o_snap.BaseBalanceAccountId
			order.QuoteBalanceAccountId = o_snap.QuoteBalanceAccountId
			order.MarketCode = o_snap.MarketCode
			order.OpenQty = decimal.NewFromProto(*o_snap.OpenQty)
			order.CumTradeQty = decimal.NewFromProto(*o_snap.CumTradeQty)
			order.CumTradeAmount = decimal.NewFromProto(*o_snap.CumTradeAmt)
			order.CumFeeAmount = decimal.NewFromProto(*o_snap.CumFeeAmt)
			order.CumTaxAmount = decimal.NewFromProto(*o_snap.CumTaxAmt)
			order.UserId = o_snap.UserId
			order.SenderCompId = o_snap.SenderCompId
			order.EarmarkAmount = decimal.NewFromProto(*o_snap.EarmarkAmt)
			order.EarmarkFee = decimal.NewFromProto(*o_snap.EarmarkFeeAmt)
			order.EarmarkTax = decimal.NewFromProto(*o_snap.EarmarkTaxAmt)
			order.TraceId = o_snap.TraceId
			order.TaxId = o_snap.TaxId
			order.MakerFeeRate = decimal.NewFromProto(*o_snap.MakerFeeRate)
			order.TakerFeeRate = decimal.NewFromProto(*o_snap.TakerFeeRate)
			order.ExpireTime = o_snap.ExpireTime
			order.CreateTime = o_snap.CreateTime
			order.CancelReason = o_snap.CancelReason
			order.MinQty = decimal.NewFromProto(*o_snap.MinQty)
			order.FeeStructures, order.TaxStructures = parse_fee_tax_structure(o_snap.HxFeesResp)
			order.TimerId = o_snap.TimerId
			order.Side, err = utils.ProtoToSide(o_snap.Side)
			if err != nil {
				log.Warn().Msgf("Failed to convert side: %v, cl_ord_id %s", err, order.ClOrdId)
			}
			order.OrdType, err = utils.ProtoToOrdType(o_snap.OrdType)
			if err != nil {
				log.Warn().Msgf("Failed to convert ordType: %v, cl_ord_id %s", err, order.ClOrdId)
			}
			timeInForce, err := utils.ProtoToTimeInForce(o_snap.TimeInForce)
			if err != nil {
				log.Warn().Msgf("Failed to convert timeInForce: %v, cl_ord_id %s", err, order.ClOrdId)
			}
			order.TimeInForce = timeInForce
			order.OperatorCode = o_snap.OperatorCode
			ob.cl_open_orders[o_snap.ClOrdId] = order

			if order.Side == enum.Side_BUY {
				new_level := new_price_level()
				new_level.price = order.Price
				level, _ := ob.bid_levels.Get(new_level)
				if level != nil {
					free_price_level(new_level)
				} else {
					level = new_level
					level.tot_qty = decimal.Zero
					ob.bid_levels.ReplaceOrInsert(level)
				}
				level.bond_price_pct = order.BondPricePct
				//order.CreateTime = t
				level.orders.ReplaceOrInsert(order)
				old_qty := level.tot_qty
				level.tot_qty = old_qty.Add(order.OpenQty)
				if !order.Price.IsZero() {
					ob.LastBid = order.Price
				}

			} else {
				new_level := new_price_level()
				new_level.price = order.Price
				level, _ := ob.ask_levels.Get(new_level)
				if level != nil {
					free_price_level(new_level)
				} else {
					level = new_level
					level.tot_qty = decimal.Zero
					ob.ask_levels.ReplaceOrInsert(level)
				}
				level.bond_price_pct = order.BondPricePct
				// order.CreateTime = t
				level.orders.ReplaceOrInsert(order)
				old_qty := level.tot_qty
				level.tot_qty = old_qty.Add(order.OpenQty)
				if !order.Price.IsZero() {
					ob.LastAsk = order.Price
				}
			}

			// restore the expired order to timerEvent
			if order.TimerId > 0 {
				timer_events[order.TimerId] = order
			}

		}
		level, _ := ob.ask_levels.Min()
		if level != nil {
			ob.BestAsk = level.price
		} else {
			ob.BestAsk = decimal.Zero
		}
		level, _ = ob.bid_levels.Max()
		if level != nil {
			ob.BestBid = level.price
		} else {
			ob.BestBid = decimal.Zero
		}
		orderbooks[sym] = ob
		log.Debug().Msgf("Restored OB From snapshot for symbol %s", sym)
	}
}

func (s *MatchService) OnStart(cluster cluster.Cluster, image aeron.Image) {
	AeronSvcMatchConnected = true
	skipRestoreOb := viper.GetBool("disable_restore_snapshot")
	s.cluster = cluster

	log.Info().Msgf("skipRestoreOb=%t , aeron.OnStart - role=%v logPos=%d", skipRestoreOb, cluster.Role(), cluster.LogPosition())
	if skipRestoreOb {
		// 18-Nov-2024. change to use sequence instead!
		// seed := viper.GetUint64("rand_seed")
		// match_rand = rand.New(seed)
		return
	}

	if image == nil {
		log.Info().Msgf("match_svc.OnStart with no image")
		// 18-Nov-2024. change to use sequence instead!
		// seed := viper.GetUint64("rand_seed")
		// match_rand = rand.New(seed)
	} else {
		start_test_dr := time.Now()
		restored := false
		handler := func(buf *atomic.Buffer, offset int32, length int32, hdr *logbuffer.Header) {

			log.Info().Msgf("[MATCH_SNAPSHOT] %s OnStart RestoreSnapshot isLeader=%t size=%d", HOST_NAME, cluster.Role() == 2, length)

			data := buf.GetBytesArray(offset, length)
			snap := match_api.MatchSnapshot{}
			err := snap.UnmarshalVT(data)
			if err != nil {
				log.Error().Err(err).Msgf("Failed to unmarshal snapshot: %v", data)
				return
			}

			// globar var
			match_out_sequence = snap.OutSequence
			ov_in_sequence = snap.OvInSequence
			timer_count = snap.AeronScheduleTimerCount

			if snap.ServiceName != viper.GetString("service_name") {
				log.Debug().Msgf("snap servicename %s config servicename %s", snap.ServiceName, viper.GetString("service_name"))
				panic("Invalid snapshot")
			}
			log.Debug().Msgf("OnStart snap=%+v, size=%d, data=%v", snap, length, data)

			// 18-Nov-2024. change to use sequence instead!
			//match_rand = rand.New()
			//err = match_rand.UnmarshalBinary(snap.RandState)
			//if err != nil {
			//	panic("Failed to unmarshal rand state")
			//}
			RestoreExecIdSequence(snap)
			RestoreTradeIdSequence(snap)

			// restore cache from snapshot
			RestoreMarketSnapshot(snap.MarketMap)
			RestoreUserSnapshot(snap.UserMap)
			RestoreSelfTradeMapSnapshot(snap.SelfTradeMap)

			last_request_ids = snap.LastRequestIds
			if last_request_ids == nil {
				panic("Missing last request ids")
			}
			restoreOb(&snap)

			restored = true
		}

		assembler := aeron.NewFragmentAssembler(handler, 1024) //to combine fragments from polling snapshot
		idleStrategy := idlestrategy.NewDefaultBackoffIdleStrategy()
		for !image.IsEndOfStream() {
			fragmentsPolled := image.Poll(assembler.OnFragment, 100)
			if viper.GetBool("enable_test_dr") {
				end_test_dr := time.Now()
				log.Debug().Msgf("DR OnStart read from snapshot elapsed time: %v, start_test_dr: %v, end_test_dr: %v", time.Since(start_test_dr), start_test_dr, end_test_dr)
			}
			log.Info().Msgf("match_svc.OnStart with image snapshotMsgCnt=%d", fragmentsPolled)
			if restored {
				break
			}
			idleStrategy.Idle(fragmentsPolled)
		}
	}
	t := s.cluster.Time()
	log.Info().Msgf("OnStart -> CLUSTER_TIME %d, %s", t, MillisToTimeObject(t).Format(ConstDateTimeFormat24Hour))
}

/*
	if !viper.GetBool("disable_start_cod") {
        req := &match_api.Request{
            Request: &match_api.Request_Disconnect{
                Disconnect: &match_api.DisconnectReq{
                    DisconnectType: match_api.DisconnectType_DISCONNECT_TYPE_MATCH_ENGINE,
                },
            },
            RequestReceivedTime: uint64(time.Now().UnixNano()),
        }

        process_request(req,nil)
    }
*/

func (s *MatchService) OnSessionOpen(session cluster.ClientSession, timestamp int64) {
	log.Info().Msgf("match_svc.OnSessionOpen - sessionId=%d timestamp=%v", session.Id(), timestamp)
	sess := &MatchSession{}
	match_sessions[session.Id()] = sess
}

func (s *MatchService) OnSessionClose(
	session cluster.ClientSession,
	timestamp int64,
	reason codecs.CloseReasonEnum,
) {
	log.Info().Msgf("match_svc.OnSessionClose - sessionId=%d timestamp=%v reason=%v", session.Id(), timestamp, reason)
	sess := match_sessions[session.Id()]
	if sess != nil && sess.OrderValidatorName != "" {
		log.Info().Msgf("Clustered Client Order Validator name=%s, source=%s is disconnected",
			sess.OrderValidatorName, sess.Source)
		req := &match_api.Request{
			Request: &match_api.Request_Disconnect{
				Disconnect: &match_api.DisconnectReq{
					DisconnectType:     match_api.DisconnectType_DISCONNECT_TYPE_ORDER_VALIDATOR,
					OrderValidatorName: sess.OrderValidatorName,
				},
			},
			Source:              sess.Source,
			RequestReceivedTime: uint64(GetClusterTimeUnixNano()),
		}

		aeron_match_svc_send_own(s.cluster, req)
	}

	delete(match_sessions, session.Id())
}

var last_request_ids = make(map[string]uint64)

func (s *MatchService) OnSessionMessage(
	session cluster.ClientSession,
	timestamp int64,
	buffer *atomic.Buffer,
	offset int32,
	length int32,
	header *logbuffer.Header,
) {

	if session == nil {
		log.Debug().Msgf("match_svc.OnSessionMessage - sessionId=%s timestamp=%v length=%d offset=%d", "self", timestamp, length, offset)
	} else {
		log.Debug().Msgf("match_svc.OnSessionMessage - sessionId=%d timestamp=%v length=%d offset=%d", session.Id(), timestamp, length, offset)
	}

	req := match_api.Request{}
	buffer.GetBytes(offset, svc_match_read_buf[:length])
	err := req.UnmarshalVT(svc_match_read_buf[:length])
	//err := req.UnmarshalVTUnsafe(svc_match_read_buf[:length])
	if err != nil {
		log.Error().Err(err).Msgf("Failed to unmarshal message: %v", svc_match_read_buf[:length])
		return
	}

	if !disable_check_duplicate {
		last_request_id, ok := last_request_ids[req.Source]
		if ok && last_request_id >= req.RequestId {
			log.Warn().Msgf("Skipping duplicate request: %v", req)
			return
		}
	}
	last_request_ids[req.Source] = req.RequestId

	if session != nil {
		sess := match_sessions[session.Id()]
		if sess != nil && req.OrderValidatorName != "" {
			sess.Source = req.Source
			sess.OrderValidatorName = req.OrderValidatorName
		}
	}

	process_request(&req, session)
}

func aeron_match_svc_send_own(c cluster.Cluster, req *match_api.Request) {
	size, err := req.MarshalToSizedBufferVT(svc_match_send_buf)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to encode request")
	}
	//buf := atomic.MakeBuffer(svc_match_send_buf[:size])
	buf := atomic.MakeBuffer(svc_match_send_buf[len(svc_match_send_buf)-size:])

	for offerCnt := 1; ; offerCnt++ {
		ret := c.Offer(buf, 0, int32(size))
		success := false
		switch ret {
		case aeron.NotConnected:
			log.Info().Msgf("aeron_match_svc_send_own: not connected yet (offerCnt=%d)", offerCnt)
			time.Sleep(1 * time.Second)
		case aeron.AdminAction:
			log.Info().Msgf("aeron_match_svc_send_own: admin action (offerCnt=%d)", offerCnt)
		case aeron.BackPressured:
			log.Info().Msgf("aeron_match_svc_send_own: back pressured (offerCnt=%d)", offerCnt)
		default:
			if ret < 0 {
				log.Error().Msgf("aeron_match_svc_send_own: unrecognized code: %d (offerCnt=%d)", ret, offerCnt)
				time.Sleep(1 * time.Second)
			} else {
				log.Debug().Msgf("aeron_match_svc_send_own: success")
				success = true
			}
		}
		if success {
			break
		}
	}
}

func aeron_match_svc_send(session cluster.ClientSession, resp *match_api.Response) {
	vtSize := resp.SizeVT()
	buf_to_use := svc_match_send_buf
	if vtSize > len(svc_match_send_buf) {
		buf_to_use = make([]byte, vtSize)
	}
	size, err := resp.MarshalToSizedBufferVT(buf_to_use)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to encode response")
	}
	//buf := atomic.MakeBuffer(svc_match_send_buf[:size])
	buf := atomic.MakeBuffer(buf_to_use[len(buf_to_use)-size:])

	for offerCnt := 1; ; offerCnt++ {
		ret := session.Offer(buf, 0, int32(size), nil)
		success := false
		switch ret {
		case aeron.NotConnected:
			log.Info().Msgf("aeron_match_svc_send: not connected yet (offerCnt=%d)", offerCnt)
			time.Sleep(1 * time.Second)
		case aeron.AdminAction:
			log.Info().Msgf("aeron_match_svc_send: admin action (offerCnt=%d)", offerCnt)
		case aeron.BackPressured:
			log.Info().Msgf("aeron_match_svc_send: back pressured (offerCnt=%d)", offerCnt)
		default:
			if ret < 0 {
				log.Error().Msgf("aeron_match_svc_send: unrecognized code: %d (offerCnt=%d)", ret, offerCnt)
				time.Sleep(1 * time.Second)
			} else {
				log.Debug().Msgf("aeron_match_svc_send: success")
				success = true
			}
		}
		if success {
			break
		}
	}
}

// Refer to https://aeroncookbook.com/aeron-cluster/cluster-timers/
// cluster.ScheduleTimer could return false
func aeron_schedule_timer(correlationId int64, deadline int64) bool {
	log.Debug().Msgf("aeron_schedule_timer correlationId=%d deadline=%v", correlationId, deadline)
	ok := aeron_match_service.cluster.ScheduleTimer(correlationId, deadline)
	if !ok {
		log.Error().Msgf("aeron_schedule_timer correlationId=%d FAILED!", correlationId)
	}
	return ok
}

func aeron_cancel_timer(correlationId int64) bool {
	ok := aeron_match_service.cluster.CancelTimer(correlationId)
	if !ok {
		log.Error().Msgf("aeron_cancel_timer correlationId=%d FAILED!", correlationId)
	}
	return ok
}

func (s *MatchService) OnTimerEvent(correlationId, timestamp int64) {
	log.Debug().Msgf("match_svc.OnTimerEvent - correlationId=%d timestamp=%v", correlationId, timestamp)
	t := s.cluster.Time()
	log.Info().Msgf("OnTimerEvent -> CLUSTER_TIME %d, %s", t, MillisToTimeObject(t).Format(ConstDateTimeFormat24Hour))
	process_timer_event(correlationId, timestamp)
}

func (s *MatchService) OnTakeSnapshot(publication *aeron.Publication) {
	log.Info().Msgf("OnTakeSnapshot %s isLeader=%t match_svc.OnTakeSnapshot - streamId=%d sessionId=%d",
		HOST_NAME, svc_match_role == cluster.Leader, publication.StreamID(), publication.SessionID())

	startTime := time.Now()
	start_test_dr := time.Now()

	snap := match_api.MatchSnapshot{}

	// global var
	snap.ServiceName = service_name
	snap.OutSequence = match_out_sequence
	snap.OvInSequence = ov_in_sequence
	snap.AeronScheduleTimerCount = timer_count

	var err error

	//  18-Nov-2024. change to use sequence instead!
	//snap.RandState, err = match_rand.MarshalBinary()
	//if err != nil {
	//	panic("Failed to marshal rand state")
	//}
	TakeExecIdSequenceSnapshot(&snap)
	TakeTradeIdSequenceSnapshot(&snap)

	// take cache to snapshot
	TakeMarketSnapshot(&snap)
	TakeUserSnapshot(&snap)
	TakeSelfTradeMapSnapshot(&snap)

	// reason when deserialize is become nil if the map is empty causing nil pointer during onStart()
	if len(last_request_ids) == 0 {
		log.Info().Msgf("last_request_ids map is empty")
		last_request_ids["dummy"] = 0
		snap.LastRequestIds = last_request_ids
	} else {
		snap.LastRequestIds = last_request_ids
	}

	for _, ob := range orderbooks {
		ob_snap := &match_api.OrderbookSnapshot{}
		ob_snap.Symbol = ob.Symbol
		ob_snap.OpenOrders = make([]*match_api.SnapshotOrder, 0, len(ob.cl_open_orders))
		ob_snap.LastTradePrice = ob.LastTradePrice.ToProtoAlloc()
		ob_snap.LastTradeQty = ob.LastTradeQty.ToProtoAlloc()
		ob_snap.LastTradeTime = ob.LastTradeTime
		ob_snap.NextPriceOffset = ob.next_price_offset
		for _, o := range ob.cl_open_orders {
			order_snap := &match_api.SnapshotOrder{}
			order_snap.OrderId = o.OrderId
			order_snap.ClOrdId = o.ClOrdId
			order_snap.OrigClOrdId = o.OrigClOrdId
			order_snap.Symbol = o.Symbol
			order_snap.Side = utils.SideToProto(o.Side)
			order_snap.OrdType = utils.OrdTypeToProto(o.OrdType)
			order_snap.Price = o.Price.ToProtoAlloc()
			order_snap.Qty = o.Qty.ToProtoAlloc()
			order_snap.TimeInForce = utils.TimeInForceToProto(o.TimeInForce)
			order_snap.HoldingAccountId = o.HoldingAccountId
			order_snap.ServiceAccountId = o.ServiceAccountId
			order_snap.BaseBalanceAccountId = o.BaseBalanceAccountId
			order_snap.QuoteBalanceAccountId = o.QuoteBalanceAccountId
			order_snap.MarketCode = o.MarketCode
			order_snap.OpenQty = o.OpenQty.ToProtoAlloc()
			order_snap.CumTradeQty = o.CumTradeQty.ToProtoAlloc()
			order_snap.CumTradeAmt = o.CumTradeAmount.ToProtoAlloc()
			order_snap.CumFeeAmt = o.CumFeeAmount.ToProtoAlloc()
			order_snap.CumTaxAmt = o.CumTaxAmount.ToProtoAlloc()
			order_snap.UserId = o.UserId
			order_snap.SenderCompId = o.SenderCompId
			order_snap.EarmarkAmt = o.EarmarkAmount.ToProtoAlloc()
			order_snap.EarmarkFeeAmt = o.EarmarkFee.ToProtoAlloc()
			order_snap.EarmarkTaxAmt = o.EarmarkTax.ToProtoAlloc()
			order_snap.TraceId = o.TraceId
			order_snap.TaxId = o.TaxId
			order_snap.MakerFeeRate = o.MakerFeeRate.ToProtoAlloc()
			order_snap.TakerFeeRate = o.TakerFeeRate.ToProtoAlloc()
			order_snap.ExpireTime = o.ExpireTime
			order_snap.CreateTime = o.CreateTime
			order_snap.CancelReason = o.CancelReason
			order_snap.MinQty = o.MinQty.ToProtoAlloc()
			order_snap.HxFeesResp = convert_fee_tax_structure(o.FeeStructures, o.TaxStructures)
			order_snap.PreventSelfTrade = o.PreventSelfTrade
			order_snap.TimerId = o.TimerId
			order_snap.BondPricePct = o.BondPricePct
			order_snap.OperatorCode = o.OperatorCode
			ob_snap.OpenOrders = append(ob_snap.OpenOrders, order_snap)
		}
		snap.Orderbooks = append(snap.Orderbooks, ob_snap)
	}
	// TODO: save market routines
	log.Debug().Msgf("OnTakeSnapshot latest snap %+v", snap)
	size := snap.SizeVT()
	data := make([]byte, size)
	_, err = snap.MarshalToVT(data)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to marshal")
	}

	log.Debug().Msgf("[MATCH_SNAPSHOT] %s OnTakeSnapshot isLeader=%t size=%d data=%d", HOST_NAME, svc_match_role == cluster.Leader, size, data)
	buf := atomic.MakeBuffer(data)
	for {
		result := publication.Offer(buf, 0, int32(size), nil)
		if result >= 0 {
			if viper.GetBool("enable_test_dr") {
				end_test_dr := time.Now()
				log.Debug().Msgf("DR OnTakeSnapshot elapsed time: %v, start_test_dr: %v, end_test_dr: %v", time.Since(start_test_dr), start_test_dr, end_test_dr)
			}
			log.Info().Msgf("[MATCH_SNAPSHOT] %s OnTakeSnapshot isLeader=%t size=%d publication.Offer SUCCESS! took=%d ms",
				HOST_NAME, svc_match_role == cluster.Leader, size, time.Since(startTime).Milliseconds())
			return
		} else if result == aeron.BackPressured || result == aeron.AdminAction {
			s.cluster.IdleStrategy().Idle(0)
		} else {
			log.Warn().Msgf("WARNING: OnTakeSnapshot offer failed - result=%v", result)
		}
	}

}

// if the node is already follower then this func would not get invoked
func (s *MatchService) OnRoleChange(role cluster.Role) {
	log.Info().Msgf("match_svc.OnRoleChange - role=%v", role)
	svc_match_role = role
	t := s.cluster.Time()
	log.Info().Msgf("OnRoleChange -> CLUSTER_TIME %d, %s", t, MillisToTimeObject(t).Format(ConstDateTimeFormat24Hour))
}

func (s *MatchService) OnTerminate(cluster cluster.Cluster) {
	log.Info().Msgf("match_svc.OnTerminate - role=%v logPos=%d", cluster.Role(), cluster.LogPosition())
	AeronSvcMatchConnected = false
	t := s.cluster.Time()
	log.Info().Msgf("OnTerminate -> CLUSTER_TIME %d, %s", t, MillisToTimeObject(t).Format(ConstDateTimeFormat24Hour))
}

// this event will get the invoked for all follower's for every leadership event
func (s *MatchService) OnNewLeadershipTermEvent(
	leadershipTermId int64,
	logPosition int64,
	timestamp int64,
	termBaseLogPosition int64,
	leaderMemberId int32,
	logSessionId int32,
	timeUnit codecs.ClusterTimeUnitEnum,
	appVersion int32,
) {
	t := s.cluster.Time()
	log.Info().Msgf("OnNewLeadershipTermEvent -> CLUSTER_TIME %d, %s", t, MillisToTimeObject(t).Format(ConstDateTimeFormat24Hour))
	AeronSvcMatchConnected = true
	log.Info().Msgf("match_svc.OnNewLeadershipTermEvent - leaderTermId=%d logPos=%d time=%d termBase=%d leaderId=%d logSessionId=%d timeUnit=%v appVer=%d",
		leadershipTermId, logPosition, timestamp, termBaseLogPosition, leaderMemberId, logSessionId, timeUnit, appVersion)
	req := &match_api.Request{
		Request: &match_api.Request_Disconnect{
			Disconnect: &match_api.DisconnectReq{
				DisconnectType: match_api.DisconnectType_DISCONNECT_TYPE_MATCH_ENGINE,
			},
		},
		RequestReceivedTime: uint64(GetClusterTimeUnixNano()),
	}
	process_request(req, nil)
}

func aeron_svc_match_take_snapshot(ignoreSentry bool) {
	log.Info().Msgf("[MATCH_SNAPSHOT] %s START aeron_svc_match_take_snapshot isLeader=%t ClusterTool cmd.CombinedOutput",
		HOST_NAME, svc_match_role == cluster.Leader)
	startTime := time.Now()
	jar_path := viper.GetString("aeron_jar_path")
	cluster_dir := viper.GetString("aeron_cluster_dir")
	cmd := exec.Command("java", "-cp", jar_path, "io.aeron.cluster.ClusterTool", cluster_dir, "snapshot")
	output, err := cmd.CombinedOutput()
	if err != nil {
		errOutputMsg := string(output)
		if !strings.Contains(errOutputMsg, "node is not the leader") {
			if !strings.Contains(errOutputMsg, "exit status 255") {
				if !ignoreSentry && sentry_enabled {
					utils.SendToSentry(err)
				}
			}
		}
		if strings.Contains(errOutputMsg, "Timed out querying cluster") ||
			strings.Contains(errOutputMsg,"Unable to SNAPSHOT as the state of the consensus module is CLOSED, but needs to be ACTIVE") {
			log.Error().Msgf("host=%s Exit Application! %s", HOST_NAME, err.Error())
			os.Exit(1)
		}
		log.Info().Msgf("[MATCH_SNAPSHOT_ERROR] %s aeron_svc_match_take_snapshot isLeader=%t ClusterTool cmd.CombinedOutput",
			HOST_NAME, svc_match_role == cluster.Leader)
		return
	}
	bytesLength := 0
	if output != nil {
		bytesLength = len(output)
	}
	log.Info().Msgf("[MATCH_SNAPSHOT] %s FINISH aeron_svc_match_take_snapshot isLeader=%t ClusterTool cmd.CombinedOutput bytesSize=%d OK! tooks=%d ms",
		HOST_NAME, svc_match_role == cluster.Leader, bytesLength, time.Since(startTime).Milliseconds())
}

func aeron_svc_match_start(exitChan chan bool) {
	ctx := aeron.NewContext()
	aeron_dir := viper.GetString("aeron_dir")
	if aeron_dir == "" {
		log.Fatal().Msg("aeron_dir not set")
	}
	ctx.AeronDir(aeron_dir)

	opts := cluster.NewOptions()
	opts.ClusterDir = viper.GetString("aeron_cluster_dir")
	if opts.ClusterDir == "" {
		log.Fatal().Msg("aeron_cluster_dir not set")
	}
	opts.ClusterId = viper.GetInt32("aeron_cluster_id")
	opts.ServiceId = viper.GetInt32("aeron_service_id")
	if viper.GetString("log_level") == "debug" {
		opts.Loglevel = zapcore.DebugLevel
	}

	chClusterConnectionIssue := make(chan bool)
	var clusterConError error
	ctx.ErrorHandler(func(err error) {
		log.Err(err).Msgf("host=%s Aeron Error Detected", HOST_NAME)
		if strings.Contains(err.Error(), "timeout") ||
			strings.Contains(err.Error(), "Session closed") ||
			strings.Contains(err.Error(), "Connection lost") {
			log.Err(err).Msg("Aeron Agent Disconnected from Cluster! Probe will failed...")
			clusterConError = err
			chClusterConnectionIssue <- true
		}
	})
	go func() {
		for isHasClusterConnIssue := range chClusterConnectionIssue {
			AeronSvcMatchConnected = !isHasClusterConnIssue
			ExitApplication(clusterConError.Error())
		}
	}()

	aeron_match_service = &MatchService{}
	agent, err := cluster.NewClusteredServiceAgent(ctx, opts, aeron_match_service)
	if err != nil {
		panic(err)
	}

	wg := sync.WaitGroup{}
	wg.Add(1)
	go func() {
		log.Info().Msgf("Starting areon clustered matching engine service...")
		wg.Done()
		err = agent.StartAndRun()
		if err != nil {
			panic(err)
		}
	}()
	wg.Wait()
}

package main

import (
	"context"
	"crypto/tls"
	"database/sql"
	"fmt"
	"sync"
	"time"

	"github.com/goccy/go-json"
	//"github.com/google/btree"
	"me_two/pkg/btree"

	"github.com/quickfixgo/enum"

	_ "github.com/go-sql-driver/mysql"
	"github.com/gomodule/redigo/redis"
	"github.com/rs/zerolog/log"
	"github.com/spf13/viper"

	"me_two/pkg/decimal"
	"me_two/pkg/match_api"

	//"me_two/pkg/types"
	"me_two/pkg/utils"

	"github.com/lirm/aeron-go/cluster"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/trace"
	"pgregory.net/rand"
)

var TaxDivisor, _ = decimal.NewFromString("100")

type Order struct {
	RequestType match_api.RequestType
	CreateTime  uint64
	OrderId     string
	ClOrdId     string
	OrigClOrdId string
	Symbol      string
	MarketCode  string
	Side        enum.Side
	OrdType     enum.OrdType
	Qty         decimal.Decimal
	Price       decimal.Decimal
	OrdStatus   enum.OrdStatus
	OpenQty     decimal.Decimal
	//FillQty               decimal.Decimal
	FillAvgPrice          decimal.Decimal
	LastExecQty           decimal.Decimal
	LastExecPrice         decimal.Decimal
	SenderCompId          string
	TimeInForce           enum.TimeInForce
	ExpireTime            uint64
	HoldingAccountId      string
	ServiceAccountId      string
	QuoteBalanceAccountId string
	BaseBalanceAccountId  string
	//HoldingAccount        *HoldingAccount
	//ServiceAccount        *ServiceAccount
	//BaseAccount      *BalanceAccount
	//QuoteAccount     *BalanceAccount
	EarmarkAmount       decimal.Decimal
	EarmarkFee          decimal.Decimal
	EarmarkTax          decimal.Decimal
	FeeCurrency         string
	CumTradeQty         decimal.Decimal
	CumTradeAmount      decimal.Decimal
	CumFeeAmount        decimal.Decimal
	CumTaxAmount        decimal.Decimal
	MakerFeeRate        decimal.Decimal
	TakerFeeRate        decimal.Decimal
	TaxRate             decimal.Decimal
	TaxId               uint64
	FeeStructures       []*FeeStructure
	TaxStructures       []*TaxStructure
	Ctx                 context.Context
	Source              string
	RequestId           uint64
	UserId              string
	Orderbook           *OrderBook
	TraceId             string
	PreventSelfTrade    bool
	CancelOnDisconnect  bool
	TimerId             int64
	MinQty              decimal.Decimal
	CancelReason        match_api.CancelReason
	GatewayName         string
	OrderValidatorName  string
	PaidInterest        decimal.Decimal
	LastPaidInterest    decimal.Decimal
	ContractAccrIntAmt  decimal.Decimal
	IsTradeOnCleanPrice bool
	BondPricePct        string
	BondNominalAmount   decimal.Decimal
	OperatorCode        string
	CancelOrderIgnoreRoutineCheck bool
}

type PriceLevelUpdateParams struct {
	Ctx          context.Context
	Symbol       string
	Side         enum.Side
	Price        decimal.Decimal
	NewQty       decimal.Decimal
	OldQty       decimal.Decimal
	Depth        int
	Time         uint64
	NumOrders    int
	Ob           *OrderBook
	Msg          *KafkaMsg
	BondPricePct string
}

func (o *Order) GetTradeTaxPercentage() (decimal.Decimal, bool) {
	if o.TaxStructures == nil || len(o.TaxStructures) == 0 {
		return decimal.Zero, false
	}
	now := GetClusterTime().Format(ConstDateTimeFormat24Hour)
	for _, tax := range o.TaxStructures {
		if now >= tax.StartDatetimeUTC && now <= tax.EndDatetimeUTC {
			return tax.Percentage, true
		}
	}
	return decimal.Zero, false
}

var timer_count int64
var timer_events = make(map[int64]*Order)

type Trade struct {
	TradeId     uint64          `json:"trade_id,omitempty"`
	Symbol      string          `json:"symbol,omitempty"`
	Side        enum.Side       `json:"side,omitempty"`
	Qty         decimal.Decimal `json:"qty,omitempty"`
	Price       decimal.Decimal `json:"price,omitempty"`
	MakeOrderId uint64          `json:"make_order_id,omitempty"`
	TakeOrderId uint64          `json:"take_order_id,omitempty"`
	CreateTime  time.Time       `json:"time,omitempty"`
}

type PriceLevelUpdate struct {
	Symbol string          `json:"symbol,omitempty"`
	Side   enum.Side       `json:"side,omitempty"`
	Price  decimal.Decimal `json:"price,omitempty"`
	Qty    decimal.Decimal `json:"qty,omitempty"`
	Time   time.Time       `json:"time,omitempty"`
	Depth  int             `json:"depth,omitempty"`
	ctx    context.Context
}

type PriceLevel struct {
	orders         *btree.BTreeG[*Order]
	price          decimal.Decimal
	tot_qty        decimal.Decimal
	time           uint64
	bond_price_pct string
}

type CombinedPriceLevel struct {
	Price     decimal.Decimal
	BidQty    decimal.Decimal
	AskQty    decimal.Decimal
	CumBidQty decimal.Decimal
	CumAskQty decimal.Decimal
}

type OrderBook struct {
	Symbol            string
	bid_levels        *btree.BTreeG[*PriceLevel]
	ask_levels        *btree.BTreeG[*PriceLevel]
	mutex             sync.RWMutex
	SpinLock          utils.SpinLock // XXX: test
	cl_open_orders    map[string]*Order
	Market            *Market
	PriceDecimals     int
	PriceMult         decimal.Decimal
	QtyDecimals       int
	QtyMult           decimal.Decimal
	QtyMin            decimal.Decimal
	QtyMax            decimal.Decimal
	MakerFeeRate      decimal.Decimal
	TakerFeeRate      decimal.Decimal
	next_price_offset uint64
	Rand              *rand.Rand
	BestBid           decimal.Decimal
	BestAsk           decimal.Decimal
	LastTradePrice    decimal.Decimal
	LastTradeTime     uint64
	LastBid           decimal.Decimal
	LastAsk           decimal.Decimal
	LastTradeQty      decimal.Decimal
}

type Routine struct {
	RoutineId           int64
	MarketId            string
	StateName           string
	AllowSelfTrade      bool
	AllowTimeInForceGTC bool
	AllowTimeInForceGTD bool
	AllowTimeInForceIOC bool
	AllowTimeInForceDAY bool
	AllowOrdTypeMarket  bool
	AllowOrdTypeLimit   bool
	AllowCreate         bool
	AllowCancel         bool
	AllowAmend          bool
	AllowMatch          bool
	UseEqPrice          bool
}

type Market struct {
	ID                         int64
	Name                       string
	Code                       string
	MarketId                   string // Code
	Timezone                   *time.Location
	ActiveRoutine              *Routine
	AllowSelfTrade             bool
	OperatorID                 int64
	OperatorCode               string
	EnableFees                 sql.NullBool
	EnableRiskControl          sql.NullBool
	SkipSellFeesEarmark        sql.NullBool
	EnableCustody              sql.NullBool
	PositionKeeping            sql.NullBool
	EnableMarketSurveillance   sql.NullBool
	ClobOrderTypes             string
	OrderTypes                 []string
	MarketTifs                 []string
	LimitTifs                  []string
	StopTifs                   []string
	OrderExpiryTime            string // HH:mm:dd in mktTimeZone TIme
	DayOrderUTCExpiryTime      int64
	EnableExtSettlementConfirm bool
	MarketModel                string
}

// var routines = make(map[string]*Routine)
var orderbooks = make(map[string]*OrderBook)
var fix_price_level_updates = make(chan *PriceLevelUpdate, 1000)
var http_price_level_updates = make(chan *PriceLevelUpdate, 1000)
var match_delay int

// 18-Nov-2024. change to use sequence instead!
//var match_rand *rand.Rand

const (
	RequestNewOrder int = iota
	RequestCancelOrder
	RequestEditOrder
)

type User struct {
	UserId       string
	CodEnabled   bool
	SenderCompId string
}

/*const (
	RespExecReport int = iota
	RespTradeReport
	RespPriceUpdate
)

type Report struct {
	ReportType  int
	ReportId    uint64
	Order       Order
	Trade       Trade
	PriceUpdate PriceLevelUpdate
	ctx         context.Context
}*/

var execReportPool = sync.Pool{
	New: func() interface{} {
		return &match_api.Response{
			Response: &match_api.Response_ExecReport{
				ExecReport: &match_api.ExecReport{
					Qty:                   &match_api.UDec128{},
					Price:                 &match_api.UDec128{},
					OpenQty:               &match_api.UDec128{},
					FillQty:               &match_api.UDec128{},
					EarmarkAmt:            &match_api.UDec128{},
					EarmarkFeeAmt:         &match_api.UDec128{},
					EarmarkTaxAmt:         &match_api.UDec128{},
					CumFillAmt:            &match_api.UDec128{},
					CumFillFeeAmt:         &match_api.UDec128{},
					CumFillTaxAmt:         &match_api.UDec128{},
					LastFillQty:           &match_api.UDec128{},
					LastFillPrice:         &match_api.UDec128{},
					FillFeeAmt:            &match_api.UDec128{},
					FillTaxAmt:            &match_api.UDec128{},
					FillFeeFromEarmarkAmt: &match_api.UDec128{},
					FillFeeFromTradeAmt:   &match_api.UDec128{},
					FillTaxFromEarmarkAmt: &match_api.UDec128{},
					FillTaxFromTradeAmt:   &match_api.UDec128{},
				},
			},
		}
	},
}

func copy_exec_report(dst *match_api.ExecReport, src *match_api.ExecReport) {
	dst.ClOrdId = src.ClOrdId
	dst.SenderCompId = src.SenderCompId
	*dst.Qty = *src.Qty
	*dst.Price = *src.Price
	*dst.OpenQty = *src.OpenQty
	*dst.FillQty = *src.FillQty
	*dst.EarmarkAmt = *src.EarmarkAmt
	*dst.EarmarkFeeAmt = *src.EarmarkFeeAmt
	*dst.EarmarkTaxAmt = *src.EarmarkTaxAmt
	*dst.CumFillAmt = *src.CumFillAmt
	*dst.CumFillFeeAmt = *src.CumFillFeeAmt
	*dst.CumFillTaxAmt = *src.CumFillTaxAmt
	*dst.LastFillQty = *src.LastFillQty
	*dst.LastFillPrice = *src.LastFillPrice
	*dst.FillFeeAmt = *src.FillFeeAmt
	*dst.FillTaxAmt = *src.FillTaxAmt
}

var tradePool = sync.Pool{
	New: func() interface{} {
		return &match_api.Response{
			Response: &match_api.Response_Trade{
				Trade: &match_api.Trade{},
			},
		}
	},
}

var priceUpdatePool = sync.Pool{
	New: func() interface{} {
		return &match_api.Response{
			Response: &match_api.Response_PriceUpdate{
				PriceUpdate: &match_api.PriceUpdate{
					Price:          &match_api.UDec128{},
					Qty:            &match_api.UDec128{},
					OldQty:         &match_api.UDec128{},
					LastTradePrice: &match_api.UDec128{},
					LastTradeQty:   &match_api.UDec128{},
				},
			},
		}
	},
}

var balanceUpdatePool = sync.Pool{
	New: func() interface{} {
		return &match_api.Response{
			Response: &match_api.Response_BalanceUpdate{
				BalanceUpdate: &match_api.BalanceUpdate{
					UpdateAmount: &match_api.UDec128{},
					BalanceAvail: &match_api.UDec128{},
					BalanceTotal: &match_api.UDec128{},
				},
			},
		}
	},
}

//var alloc_reports=make(map[*match_api.Response]bool)

var exec_report_lock utils.SpinLock

func new_exec_report() *match_api.Response {
	exec_report_lock.Lock()
	defer exec_report_lock.Unlock()
	metrics.AllocExecReports++
	report := execReportPool.Get().(*match_api.Response)
	//*(report.Response.(*match_api.Response_ExecReport).ExecReport) = match_api.ExecReport{} // DON'T CLEAR
	return report
}

func free_exec_report(report *match_api.Response) {
	exec_report_lock.Lock()
	defer exec_report_lock.Unlock()
	metrics.AllocExecReports--
	execReportPool.Put(report)
}

/*func new_trade() *match_api.Response {
	metrics.AllocTrades++
	report := tradePool.Get().(*match_api.Response)
	return report
}

func free_trade(report *match_api.Response) {
	metrics.AllocTrades--
	tradePool.Put(report)
}*/

var price_update_lock utils.SpinLock

func new_price_update() *match_api.Response {
	price_update_lock.Lock()
	defer price_update_lock.Unlock()
	metrics.AllocPriceUpdates++
	report := priceUpdatePool.Get().(*match_api.Response)
	//*(report.Response.(*match_api.Response_PriceUpdate).PriceUpdate) = match_api.PriceUpdate{} // DON'T CLEAR
	return report
}

func free_price_update(report *match_api.Response) {
	price_update_lock.Lock()
	defer price_update_lock.Unlock()
	metrics.AllocPriceUpdates--
	priceUpdatePool.Put(report)
}

//var balance_update_lock utils.SpinLock

/*func new_balance_update() *match_api.Response {
	balance_update_lock.Lock()
	defer balance_update_lock.Unlock()
	metrics.AllocBalanceUpdates++
	report := balanceUpdatePool.Get().(*match_api.Response)
	//*(report.Response.(*match_api.Response_BalanceUpdate).BalanceUpdate) = match_api.BalanceUpdate{} // DON'T CLEAR
	return report
}*/

/*func free_balance_update(report *match_api.Response) {
	balance_update_lock.Lock()
	defer balance_update_lock.Unlock()
	metrics.AllocBalanceUpdates--
	balanceUpdatePool.Put(report)
}*/

func self_trade_allowed(holdingAcctId string, marketCode string) bool {
	mkt, ok := markets[marketCode]
	if !ok {
		log.Debug().Msgf("[ALLOW_SELF_TRADE] holdingAcctId=%s market=%s NOT_ALLOWED. MKT Not Found", holdingAcctId, marketCode)
		return false
	}
	mktAllowSelfTrading := mkt.AllowSelfTrade

	if self_trade_map == nil {
		log.Debug().Msgf("[ALLOW_SELF_TRADE] holdingAcctId=%s market=%s NOT_ALLOWED. self_trade_map is nil", holdingAcctId, marketCode)
		return false
	}

	acc_name, ok := self_trade_map[holdingAcctId]
	if !ok {
		log.Debug().Msgf("[ALLOW_SELF_TRADE] holdingAcctId=%s market=%s NOT_ALLOWED. holdingAcctId not found", holdingAcctId, marketCode)
		return false
	}

	is_allowed, ok := acc_name[marketCode]
	if !ok {
		log.Debug().Msgf("[ALLOW_SELF_TRADE] holdingAcctId=%s market=%s NOT_ALLOWED. acc_name not contain market", holdingAcctId, marketCode)
		return false
	}
	isSelfTradeAllowed := is_allowed && mktAllowSelfTrading

	log.Debug().Msgf("[ALLOW_SELF_TRADE] holdingAcctId=%s market=%s isSelfTradeAllowed=%t is_allowed=%t mktAllowSelfTrading=%t",
		holdingAcctId, marketCode, isSelfTradeAllowed, is_allowed, mktAllowSelfTrading)

	return isSelfTradeAllowed
}

func send_order_report_new(ctx context.Context, order *Order, ob *OrderBook, msg *KafkaMsg, hx_order_fee_details map[string]*match_api.UDec128) {
	//log.Debug().Msgf("send_order_report_new: %s", order.OrderId)
	side := utils.SideToProto(order.Side)
	ord_status := utils.OrdStatusToProto(order.OrdStatus)
	ord_type := utils.OrdTypeToProto(order.OrdType)
	time_in_force := utils.TimeInForceToProto(order.TimeInForce)

	if !disable_send_exec_report {
		report := new_exec_report()
		exec := report.Response.(*match_api.Response_ExecReport).ExecReport
		exec.ExecType = match_api.ExecType_EXEC_TYPE_NEW
		exec.ExecId = new_rand_exec_id()
		exec.Symbol = order.Symbol
		exec.OrderId = order.OrderId
		exec.ClOrdId = order.ClOrdId
		*exec.Qty = order.Qty.ToProto()
		*exec.Price = order.Price.ToProto()
		exec.Side = side
		exec.OrdStatus = ord_status
		*exec.OpenQty = order.OpenQty.ToProto()
		*exec.FillQty = order.CumTradeQty.ToProto()
		exec.SenderCompId = order.SenderCompId
		exec.HoldingAccountId = order.HoldingAccountId
		exec.ServiceAccountId = order.ServiceAccountId
		exec.BaseBalanceAccountId = order.BaseBalanceAccountId
		exec.QuoteBalanceAccountId = order.QuoteBalanceAccountId
		exec.OrdType = ord_type
		exec.TimeInForce = time_in_force
		exec.TransactTime = uint64(GetClusterTimeUnixNano()) // move to start of request for speed
		exec.UserId = order.UserId
		exec.TraceId = order.TraceId
		*exec.EarmarkAmt = order.EarmarkAmount.ToProto()
		*exec.EarmarkFeeAmt = order.EarmarkFee.ToProto()
		*exec.EarmarkTaxAmt = order.EarmarkTax.ToProto()
		*exec.CumFillAmt = order.CumTradeAmount.ToProto()
		*exec.CumFillFeeAmt = order.CumFeeAmount.ToProto()
		*exec.CumFillTaxAmt = order.CumTaxAmount.ToProto()
		exec.ExpiryTime = order.ExpireTime
		exec.CreateTime = order.CreateTime
		exec.CancelReason = match_api.CancelReason_EMPTY_REASON
		exec.Text = ""
		exec.IsTradeOnCleanPrice = order.IsTradeOnCleanPrice
		exec.BondPricePct = order.BondPricePct

		if !order.BondNominalAmount.IsZero() {
			exec.BondNominalAmount = order.BondNominalAmount.ToProtoAlloc()
		}

		if hx_order_fee_details != nil {
			exec.OrderFeesDetails = hx_order_fee_details
			exec.Fees = []*match_api.ExecReportFee{{
				Amount:   exec.EarmarkFeeAmt,
				Currency: order.FeeCurrency,
			}}
		}
		exec.TaxId = order.TaxId
		exec.OperatorCode = order.OperatorCode
		if msg != nil {
			msg.AddReport(report)
		}
	}
}

func send_order_report_trade(ctx context.Context, order *Order, ob *OrderBook, msg *KafkaMsg, trade_id string, trade_qty, trade_price, fee_amt, tax_amt decimal.Decimal, is_taker bool, trade_fee_details map[string]*match_api.UDec128, trade_fees_charged bool, fees_charge_err_msg string, fee_from_earmark_amt, fee_from_trade_amt, tax_from_earmark_amt, tax_from_trade_amt decimal.Decimal) {
	//log.Debug().Msgf("send_order_report_trade: %s", order.OrderId)
	side := utils.SideToProto(order.Side)
	ord_status := utils.OrdStatusToProto(order.OrdStatus)
	ord_type := utils.OrdTypeToProto(order.OrdType)

	if !disable_send_exec_report {
		report := new_exec_report()
		exec := report.Response.(*match_api.Response_ExecReport).ExecReport
		exec.ExecType = match_api.ExecType_EXEC_TYPE_TRADE
		exec.ExecId = new_rand_exec_id()
		*exec.FillQty = order.CumTradeQty.ToProto()
		exec.SenderCompId = order.SenderCompId
		exec.UserId = order.UserId
		exec.HoldingAccountId = order.HoldingAccountId
		exec.ServiceAccountId = order.ServiceAccountId
		exec.TransactTime = uint64(GetClusterTimeUnixNano())
		exec.BaseBalanceAccountId = order.BaseBalanceAccountId
		exec.QuoteBalanceAccountId = order.QuoteBalanceAccountId
		exec.OrdType = ord_type
		exec.CreateTime = order.CreateTime
		exec.LastTradeId = trade_id
		*exec.LastFillQty = trade_qty.ToProto()
		*exec.LastFillPrice = trade_price.ToProto()
		exec.Symbol = order.Symbol
		exec.OrderId = order.OrderId
		exec.ClOrdId = order.ClOrdId
		*exec.Qty = order.Qty.ToProto()
		*exec.Price = order.Price.ToProto()
		exec.Side = side
		exec.OrdStatus = ord_status
		*exec.OpenQty = order.OpenQty.ToProto()

		log.Debug().Msgf("order.LastPaidInterest: %v order.PaidInterest: %v", order.LastPaidInterest, order.PaidInterest)
		if !order.LastPaidInterest.IsZero() {
			exec.LastPaidInterest = &match_api.UDec128{}
			*exec.LastPaidInterest = order.LastPaidInterest.ToProto()
		}
		if !order.PaidInterest.IsZero() {
			exec.PaidInterest = &match_api.UDec128{}
			*exec.PaidInterest = order.PaidInterest.ToProto()
		}
		exec.IsTradeOnCleanPrice = order.IsTradeOnCleanPrice
		exec.BondPricePct = order.BondPricePct
		if !order.BondNominalAmount.IsZero() {
			exec.BondNominalAmount = order.BondNominalAmount.ToProtoAlloc()
		}
		*exec.FillFeeAmt = fee_amt.ToProto()
		*exec.FillTaxAmt = tax_amt.ToProto()
		*exec.EarmarkAmt = order.EarmarkAmount.ToProto()
		*exec.EarmarkFeeAmt = order.EarmarkFee.ToProto()
		*exec.EarmarkTaxAmt = order.EarmarkTax.ToProto()
		*exec.CumFillAmt = order.CumTradeAmount.ToProto()
		*exec.CumFillFeeAmt = order.CumFeeAmount.ToProto()
		*exec.CumFillTaxAmt = order.CumTaxAmount.ToProto()
		if !order.CumTradeQty.IsZero() && !order.FillAvgPrice.IsZero() {
			exec.FillAvgPrice = &match_api.UDec128{}
			*exec.FillAvgPrice = order.FillAvgPrice.ToProto()
		}
		exec.MakerTaker = is_taker
		exec.TaxId = order.TaxId
		exec.TimeInForce = utils.TimeInForceToProto(order.TimeInForce)
		exec.CancelReason = match_api.CancelReason_EMPTY_REASON
		exec.Text = ""
		exec.MarketCode = order.MarketCode
		exec.EnableExtSettlementConfirm = ob.Market.EnableExtSettlementConfirm
		exec.TradeFeeCharged = trade_fees_charged
		exec.FeesChargeErrMsg = fees_charge_err_msg
		*exec.FillFeeFromEarmarkAmt = fee_from_earmark_amt.ToProto()
		*exec.FillFeeFromTradeAmt = fee_from_trade_amt.ToProto()
		*exec.FillTaxFromEarmarkAmt = tax_from_earmark_amt.ToProto()
		*exec.FillTaxFromTradeAmt = tax_from_trade_amt.ToProto()
		exec.OperatorCode = order.OperatorCode
		if trade_fee_details != nil {
			exec.TradeFeesDetails = trade_fee_details
		}
		if !fee_amt.IsZero() {
			exec.Fees = []*match_api.ExecReportFee{{
				Amount:   exec.FillFeeAmt,
				Currency: order.FeeCurrency,
			}}
		}
		if msg != nil {
			msg.AddReport(report)
		}
	}
}

func send_order_report_canceled(ctx context.Context, order *Order, ob *OrderBook, msg *KafkaMsg) {
	//log.Debug().Msgf("send_order_report_canceled: %s", order.OrderId)
	side := utils.SideToProto(order.Side)
	ord_status := utils.OrdStatusToProto(order.OrdStatus)

	if !disable_send_exec_report {
		report := new_exec_report()
		exec := report.Response.(*match_api.Response_ExecReport).ExecReport
		exec.TransactTime = uint64(GetClusterTimeUnixNano())
		exec.ExecType = match_api.ExecType_EXEC_TYPE_CANCELED
		exec.ExecId = new_rand_exec_id()
		exec.Symbol = order.Symbol
		*exec.EarmarkTaxAmt = order.EarmarkTax.ToProto()

		exec.OrderId = order.OrderId
		exec.ClOrdId = order.ClOrdId
		*exec.Qty = order.Qty.ToProto()
		*exec.Price = order.Price.ToProto()
		exec.Side = side
		*exec.EarmarkFeeAmt = order.EarmarkFee.ToProto()

		exec.OrdStatus = ord_status
		*exec.OpenQty = order.OpenQty.ToProto()
		*exec.FillQty = order.CumTradeQty.ToProto()
		exec.SenderCompId = order.SenderCompId
		*exec.EarmarkAmt = order.EarmarkAmount.ToProto()
		exec.UserId = order.UserId
		exec.HoldingAccountId = order.HoldingAccountId
		exec.ServiceAccountId = order.ServiceAccountId
		exec.BaseBalanceAccountId = order.BaseBalanceAccountId
		exec.QuoteBalanceAccountId = order.QuoteBalanceAccountId
		exec.CreateTime = order.CreateTime

		*exec.CumFillAmt = order.CumTradeAmount.ToProto()
		exec.TaxId = order.TaxId
		exec.TraceId = order.TraceId
		exec.CancelReason = order.CancelReason
		exec.Text = ""


		exec.OrdStatus = match_api.OrdStatus_ORD_STATUS_CANCELED
		exec.OrdType = utils.OrdTypeToProto(order.OrdType)
		exec.TimeInForce = utils.TimeInForceToProto(order.TimeInForce)
		exec.ExpiryTime = order.ExpireTime
		*exec.CumFillFeeAmt = order.CumFeeAmount.ToProto()

		exec.IsTradeOnCleanPrice = order.IsTradeOnCleanPrice
		*exec.CumFillTaxAmt = order.CumTaxAmount.ToProto()

		exec.BondPricePct = order.BondPricePct
		if !order.BondNominalAmount.IsZero() {
			exec.BondNominalAmount = order.BondNominalAmount.ToProtoAlloc()
		}
		exec.OperatorCode = order.OperatorCode
		if exec.FillAvgPrice == nil {
			exec.FillAvgPrice = &match_api.UDec128{}
		}
		*exec.FillAvgPrice = order.FillAvgPrice.ToProto()
		if msg != nil {
			msg.AddReport(report)
		}
	}
}

func send_order_report_replaced(ctx context.Context, order *Order, ob *OrderBook, msg *KafkaMsg) {
	//log.Debug().Msgf("send_order_report_replaced: %s", order.OrderId)
	side := utils.SideToProto(order.Side)
	ord_status := utils.OrdStatusToProto(order.OrdStatus)

	if !disable_send_exec_report {
		report := new_exec_report()
		exec := report.Response.(*match_api.Response_ExecReport).ExecReport
		exec.ExecType = match_api.ExecType_EXEC_TYPE_REPLACED
		exec.ExecId = new_rand_exec_id()
		exec.Symbol = order.Symbol
		*exec.Qty = order.Qty.ToProto()
		*exec.Price = order.Price.ToProto()
		exec.OrdStatus = ord_status
		exec.ClOrdId = order.ClOrdId

		*exec.OpenQty = order.OpenQty.ToProto()
		*exec.FillQty = order.CumTradeQty.ToProto()
		exec.SenderCompId = order.SenderCompId
		exec.UserId = order.UserId
		exec.HoldingAccountId = order.HoldingAccountId
		exec.OrderId = order.OrderId

		exec.ServiceAccountId = order.ServiceAccountId
		exec.BaseBalanceAccountId = order.BaseBalanceAccountId
		exec.QuoteBalanceAccountId = order.QuoteBalanceAccountId
		exec.TransactTime = uint64(GetClusterTimeUnixNano()) // move to start of request for speed
		exec.CreateTime = order.CreateTime
		*exec.EarmarkAmt = order.EarmarkAmount.ToProto()
		*exec.EarmarkFeeAmt = order.EarmarkFee.ToProto()
		*exec.EarmarkTaxAmt = order.EarmarkTax.ToProto()
		*exec.CumFillAmt = order.CumTradeAmount.ToProto()
		*exec.CumFillFeeAmt = order.CumFeeAmount.ToProto()
		*exec.CumFillTaxAmt = order.CumTaxAmount.ToProto()
		exec.TaxId = order.TaxId
		exec.OrdType = utils.OrdTypeToProto(order.OrdType)
		exec.TimeInForce = utils.TimeInForceToProto(order.TimeInForce)
		exec.ExpiryTime = order.ExpireTime
		exec.CancelReason = match_api.CancelReason_EMPTY_REASON
		exec.Side = side

		exec.Text = ""
		exec.OperatorCode = order.OperatorCode
		if msg != nil {
			msg.AddReport(report)
		}
	}
}

func send_order_report_expired(ctx context.Context, order *Order, ob *OrderBook, msg *KafkaMsg) {
	//log.Debug().Msgf("send_order_report_expired: %s", order.OrderId)
	side := utils.SideToProto(order.Side)
	ord_status := utils.OrdStatusToProto(order.OrdStatus)

	if !disable_send_exec_report {
		report := new_exec_report()
		exec := report.Response.(*match_api.Response_ExecReport).ExecReport
		exec.ExecType = match_api.ExecType_EXEC_TYPE_CANCELED
		exec.ExecId = new_rand_exec_id()
		exec.Symbol = order.Symbol
		exec.OrderId = order.OrderId
		exec.ClOrdId = order.ClOrdId
		*exec.Qty = order.Qty.ToProto()
		*exec.Price = order.Price.ToProto()
		exec.Side = side
		exec.OrdStatus = ord_status
		*exec.OpenQty = order.OpenQty.ToProto()
		*exec.FillQty = order.CumTradeQty.ToProto()
		exec.SenderCompId = order.SenderCompId
		exec.UserId = order.UserId
		exec.HoldingAccountId = order.HoldingAccountId
		exec.ServiceAccountId = order.ServiceAccountId
		exec.BaseBalanceAccountId = order.BaseBalanceAccountId
		exec.QuoteBalanceAccountId = order.QuoteBalanceAccountId

		exec.CreateTime = order.CreateTime
		*exec.EarmarkAmt = order.EarmarkAmount.ToProto()
		*exec.EarmarkFeeAmt = order.EarmarkFee.ToProto()
		*exec.EarmarkTaxAmt = order.EarmarkTax.ToProto()
		*exec.CumFillAmt = order.CumTradeAmount.ToProto()
		*exec.CumFillFeeAmt = order.CumFeeAmount.ToProto()
		*exec.CumFillTaxAmt = order.CumTaxAmount.ToProto()
		exec.TaxId = order.TaxId
		exec.CancelReason = order.CancelReason
		exec.Text = ""
		exec.OrdType = utils.OrdTypeToProto(order.OrdType)
		exec.TimeInForce = utils.TimeInForceToProto(order.TimeInForce)
		exec.ExpiryTime = order.ExpireTime
		exec.OperatorCode = order.OperatorCode
		exec.TransactTime = uint64(GetClusterTimeUnixNano())
		if msg != nil {
			msg.AddReport(report)
		}
	}
}

func update_order_trade(ctx context.Context, ob *OrderBook, order *Order, trade_id string, trade_qty decimal.Decimal, trade_price decimal.Decimal, trade_amt decimal.Decimal, lastPaidInterestAmt decimal.Decimal, is_taker bool, msg *KafkaMsg) {
	/*var receive_ba *BalanceAccount
	var receive_amt decimal.Decimal
	var spend_ba *BalanceAccount
	var spend_amt decimal.Decimal
	var scenario_receive, scenario_spend, scenario_fees, scenario_tax match_api.Scenario
	var scenario_earmark, scenario_earmark_fees, scenario_earmark_tax match_api.Scenario
	if order.Side == enum.Side_BUY {
		receive_ba = order.BaseAccount
		if receive_ba == nil {
			panic("base account is nil")
		}
		receive_amt = trade_qty
		spend_ba = order.QuoteAccount
		if spend_ba == nil {
			panic("quote account is nil")
		}
		spend_amt = trade_amt
		scenario_receive = match_api.Scenario_SCENARIO_CONFIRM_BUY_BASE
		scenario_spend = match_api.Scenario_SCENARIO_CONFIRM_BUY_QUOTE
		if is_taker {
			scenario_fees = match_api.Scenario_SCENARIO_CONFIRM_FEES_TAKER_BUY
			scenario_tax = match_api.Scenario_SCENARIO_CONFIRM_TAX_TAKER_BUY
		} else {
			scenario_fees = match_api.Scenario_SCENARIO_CONFIRM_FEES_MAKER_BUY
			scenario_tax = match_api.Scenario_SCENARIO_CONFIRM_TAX_MAKER_BUY
		}
		scenario_earmark = match_api.Scenario_SCENARIO_RETURN_EARMARK_BUY
		scenario_earmark_fees = match_api.Scenario_SCENARIO_RETURN_EARMARK_BUY_FEES
		scenario_earmark_tax = match_api.Scenario_SCENARIO_RETURN_EARMARK_BUY_TAX
	} else {
		receive_ba = order.QuoteAccount
		if receive_ba == nil {
			panic("quote account is nil")
		}
		receive_amt = trade_amt
		spend_ba = order.BaseAccount
		if spend_ba == nil {
			panic("base account is nil")
		}
		spend_amt = trade_qty
		scenario_receive = match_api.Scenario_SCENARIO_CONFIRM_SELL_QUOTE
		scenario_spend = match_api.Scenario_SCENARIO_CONFIRM_SELL_BASE
		if is_taker {
			scenario_fees = match_api.Scenario_SCENARIO_CONFIRM_FEES_TAKER_SELL
			scenario_tax = match_api.Scenario_SCENARIO_CONFIRM_TAX_TAKER_SELL
		} else {
			scenario_fees = match_api.Scenario_SCENARIO_CONFIRM_FEES_MAKER_SELL
			scenario_tax = match_api.Scenario_SCENARIO_CONFIRM_TAX_MAKER_SELL
		}
		scenario_earmark = match_api.Scenario_SCENARIO_RETURN_EARMARK_SELL
		scenario_earmark_fees = match_api.Scenario_SCENARIO_RETURN_EARMARK_SELL_FEES
		scenario_earmark_tax = match_api.Scenario_SCENARIO_RETURN_EARMARK_SELL_TAX
	}
	update_balance(ob, order, receive_ba, receive_amt, match_api.BalanceUpdateType_BALANCE_UPDATE_TOTAL_AVAIL, "Trade "+trade_id, false, match_api.ScenarioType_SCENARIO_TYPE_BALANCE, scenario_receive, msg)
	update_balance_neg(ob, order, spend_ba, spend_amt, match_api.BalanceUpdateType_BALANCE_UPDATE_TOTAL_AVAIL, "Trade "+trade_id, false, match_api.ScenarioType_SCENARIO_TYPE_BALANCE, scenario_spend, msg)
	update_balance(ob, order, spend_ba, spend_amt, match_api.BalanceUpdateType_BALANCE_UPDATE_AVAIL_ONLY, "Trade "+trade_id, false, match_api.ScenarioType_SCENARIO_TYPE_BALANCE, scenario_earmark, msg)*/

	// deduct open qty first
	order.OpenQty = order.OpenQty.Sub(trade_qty)

	fee_amt := decimal.Zero
	tax_amt := decimal.Zero
	trade_fees_charged := true
	fees_charge_err_msg := ""

	// skip sell fee feature
	// case where by Sell fill fee > order fee earmark and < traded amount, and Skip sell is turned on halfway
	// fee is took from earmark + traded quote amount
	fee_from_earmark_amt := decimal.Zero
	fee_from_trade_amt := decimal.Zero
	tax_from_earmark_amt := decimal.Zero
	tax_from_trade_amt := decimal.Zero

	// hx fees
	var trade_fee_details map[string]*match_api.UDec128
	skipSellFeeEarmark := false
	if ob.Market.SkipSellFeesEarmark.Valid {
		skipSellFeeEarmark = ob.Market.SkipSellFeesEarmark.Bool
	}
	log.Debug().Msgf("cli_ord_id %s, skipSellFeeEarmark %t, side %s", order.ClOrdId, skipSellFeeEarmark, order.Side)
	if !hx_fees_enabled || order.FeeStructures == nil {
		if !hx_fees_enabled {
			fees_charge_err_msg = "fees not enabled"
		} else {
			hasEarmarkFee := !order.EarmarkFee.IsZero()
			trade_fees_charged = false
			fees_charge_err_msg = "fee assignment not set"
			if !hasEarmarkFee {
				fees_charge_err_msg = "fees not enabled"
				if order.Side == enum.Side_SELL && skipSellFeeEarmark {
					fees_charge_err_msg = "fee assignment not set"
				}
			}
		}
	}

	if hx_fees_enabled && order.FeeStructures != nil {
		trade_fee_details = make(map[string]*match_api.UDec128)
		trade_tax_pct, has_trade_tax_conf := order.GetTradeTaxPercentage()

		// Happy Path, Earmark Fee And Tax Fully can offset traded fee and Tax
		for _, fee_struct := range order.FeeStructures {
			amt_to_use := trade_qty
			if fee_struct.DeriveOnAmount {
				amt_to_use = trade_amt
			}
			derived_trade_fee := calc_fees(amt_to_use, fee_struct)
			if !derived_trade_fee.IsZero() {
				trade_fee_details[fee_struct.Description] = derived_trade_fee.ToProtoAlloc()
				fee_amt = fee_amt.Add(derived_trade_fee)
			}
		}

		hasFeeAmt := !fee_amt.IsZero()
		if hasFeeAmt && has_trade_tax_conf {
			tax_amt = (fee_amt.Mul(trade_tax_pct)).Div(TaxDivisor)
			trade_fee_details["tax"] = tax_amt.ToProtoAlloc()
		}

		// when there is no earmark then do not charge fee
		isCommonSell := !skipSellFeeEarmark && order.Side == enum.Side_SELL
		isCommonBuy := order.Side == enum.Side_BUY
		noFeeEarmark := order.EarmarkFee.IsZero()
		doNotChargeFee := noFeeEarmark && (isCommonBuy || isCommonSell)
		if doNotChargeFee {
			fee_amt = decimal.Zero
			tax_amt = decimal.Zero
			trade_fees_charged = false
			fees_charge_err_msg = "order has no earmark fee"
		} else {
			orderFullyFilled := order.OpenQty.IsZero()
			hasFeeEarmark := !order.EarmarkFee.IsZero()
			capFeeCalculated := false
			isNormalBuy := order.Side == enum.Side_BUY
			isNormalSell := !skipSellFeeEarmark && hasFeeEarmark && order.Side == enum.Side_SELL
			if hasFeeAmt && (isNormalBuy || isNormalSell) {
				capFeeCalculated = true
				fee_amt, tax_amt = cap_fee_normal_trade(order, orderFullyFilled, fee_amt, tax_amt, trade_fee_details)
			}

			isSellFeeNotEarmarked := !capFeeCalculated && hasFeeAmt && skipSellFeeEarmark && noFeeEarmark
			if isSellFeeNotEarmarked {
				capFeeCalculated = true
				fee_amt, tax_amt, trade_fees_charged, fees_charge_err_msg = cap_fee_sell_no_earmark_fee(order, fee_amt, tax_amt, trade_fee_details, trade_amt, skipSellFeeEarmark, trade_fees_charged, fees_charge_err_msg)
			}

			isSellFeeEarmarked := !capFeeCalculated && hasFeeAmt && skipSellFeeEarmark && hasFeeEarmark
			if isSellFeeEarmarked {
				fee_amt, tax_amt,
					fee_from_earmark_amt, fee_from_trade_amt, tax_from_earmark_amt, tax_from_trade_amt,
					trade_fees_charged, fees_charge_err_msg = cap_fee_sell_has_earmark_fee(order, fee_amt, tax_amt, trade_fee_details, trade_amt, orderFullyFilled, skipSellFeeEarmark, trade_fees_charged, fees_charge_err_msg)
			}
		}
		/*fee_ba := order.QuoteAccount
		update_balance_neg(ob, order, fee_ba, fee_amt, match_api.BalanceUpdateType_BALANCE_UPDATE_TOTAL_AVAIL, "Trade Fee "+trade_id, false, match_api.ScenarioType_SCENARIO_TYPE_FEES, scenario_fees, msg)
		update_balance(ob, order, fee_ba, fee_amt, match_api.BalanceUpdateType_BALANCE_UPDATE_AVAIL_ONLY, "Trade Fee "+trade_id, false, match_api.ScenarioType_SCENARIO_TYPE_FEES, scenario_earmark_fees, msg)*/
	} else {

		if !hx_fees_enabled {
			// simple fee
			var fee_rate decimal.Decimal
			if is_taker {
				fee_rate = order.TakerFeeRate
			} else {
				fee_rate = order.MakerFeeRate
			}
			if !fee_rate.IsZero() {
				fee_amt = calc_fees_simple(trade_amt, fee_rate)
			}
			tax_rate := order.TaxRate
			if !fee_amt.IsZero() && !tax_rate.IsZero() {
				tax_amt = calc_fees_simple(fee_amt, tax_rate)
				/*tax_ba := order.QuoteAccount
				update_balance_neg(ob, order, tax_ba, tax_amt, match_api.BalanceUpdateType_BALANCE_UPDATE_TOTAL_AVAIL, "Trade Tax "+trade_id, false, match_api.ScenarioType_SCENARIO_TYPE_TAX, scenario_tax, msg)
				update_balance(ob, order, tax_ba, tax_amt, match_api.BalanceUpdateType_BALANCE_UPDATE_AVAIL_ONLY, "Trade Tax "+trade_id, false, match_api.ScenarioType_SCENARIO_TYPE_TAX, scenario_earmark_tax, msg)*/
			}
		}
	}

	order.LastExecQty = trade_qty
	order.LastExecPrice = trade_price
	order.CumTradeQty = order.CumTradeQty.Add(trade_qty)
	order.CumTradeAmount = order.CumTradeAmount.Add(trade_amt)
	order.CumFeeAmount = order.CumFeeAmount.Add(fee_amt)
	order.CumTaxAmount = order.CumTaxAmount.Add(tax_amt)

	order.LastPaidInterest = lastPaidInterestAmt
	order.PaidInterest = order.PaidInterest.Add(lastPaidInterestAmt)

	log.Debug().Msgf("ClOrdId %s, trade_qty %v, lastPaidInterest %v, PaidInterest %v", order.ClOrdId, trade_qty, lastPaidInterestAmt, order.PaidInterest)

	order.FillAvgPrice = decimal.Zero
	if order.CumTradeQty.GreaterThan(decimal.Zero) {
		order.FillAvgPrice = order.CumTradeAmount.Div(order.CumTradeQty)
	}
	log.Debug().Msgf("order fill avg price %v", order.FillAvgPrice)
	if order.OpenQty.IsZero() {
		order.OrdStatus = enum.OrdStatus_FILLED
		/*var ba *BalanceAccount
		var amt decimal.Decimal
		if order.Side == enum.Side_BUY {
			amt = order.EarmarkAmount.Sub(order.TradeAmount)
			ba = order.QuoteAccount
		} else {
			amt = order.EarmarkAmount.Sub(order.TradeQty)
			ba = order.BaseAccount
		}
		if !amt.IsZero() {
			update_balance(ob, order, ba, amt, match_api.BalanceUpdateType_BALANCE_UPDATE_AVAIL_ONLY, "Order Close "+order.OrderId, false, match_api.ScenarioType_SCENARIO_TYPE_BALANCE, scenario_earmark, msg)
		}*/
		// TODO: fee, tax earmark return
	} else if !order.CumTradeQty.IsZero() {
		order.OrdStatus = enum.OrdStatus_PARTIALLY_FILLED
	}
	send_order_report_trade(ctx, order, ob, msg, trade_id, trade_qty, trade_price, fee_amt, tax_amt, is_taker, trade_fee_details, trade_fees_charged, fees_charge_err_msg, fee_from_earmark_amt, fee_from_trade_amt, tax_from_earmark_amt, tax_from_trade_amt)
}

/*func update_balance(ob *OrderBook, order *Order, ba *BalanceAccount, amt decimal.Decimal, update_type match_api.BalanceUpdateType, ref string, check_avail bool, scenario_type match_api.ScenarioType, scenario match_api.Scenario, msg *KafkaMsg) error {
	ba.Lock.Lock()
	defer ba.Lock.Unlock()
	new_avail_bal := ba.AvailBalance.Add(amt)
	if update_type == match_api.BalanceUpdateType_BALANCE_UPDATE_TOTAL_AVAIL || update_type == match_api.BalanceUpdateType_BALANCE_UPDATE_AVAIL_ONLY {
		ba.AvailBalance = new_avail_bal
	}
	if update_type == match_api.BalanceUpdateType_BALANCE_UPDATE_TOTAL_AVAIL || update_type == match_api.BalanceUpdateType_BALANCE_UPDATE_TOTAL_ONLY {
		ba.TotalBalance = ba.TotalBalance.Add(amt)
	}
	if !disable_send_balance_update {
		resp := new_balance_update()
		bu := resp.Response.(*match_api.Response_BalanceUpdate).BalanceUpdate
		bu.BalanceAccountId = ba.BalanceAccountId
		bu.AssetSymbol = ba.AssetSymbol
		bu.OrderId = order.OrderId
		bu.Ref = ref
		bu.UpdateType = update_type
		*bu.UpdateAmount = amt.ToProto()
		*bu.BalanceAvail = ba.AvailBalance.ToProto()
		*bu.BalanceTotal = ba.TotalBalance.ToProto()
		bu.MarketCode = ob.Market.MarketId
		bu.Scenario = scenario
		bu.ScenarioType = scenario_type
		bu.BalanceUpdateId = new_rand_balance_update_id(ob.Rand)
		bu.Time = uint64(time.Now().UnixNano()) // move to start of request for speed
		if msg != nil {
			msg.AddReport(resp)
		}
	}
	return nil
}*/

/*func update_balance_neg(ob *OrderBook, order *Order, ba *BalanceAccount, amt decimal.Decimal, update_type match_api.BalanceUpdateType, ref string, check_avail bool, scenario_type match_api.ScenarioType, scenario match_api.Scenario, msg *KafkaMsg) error {
	ba.Lock.Lock()
	defer ba.Lock.Unlock()
	if check_avail && !disable_balance_check && ba.AvailBalance.LessThan(amt) {
		return fmt.Errorf("Insufficient balance: %s < %s, balance_account=%s", ba.AvailBalance.String(), amt.String(), ba.BalanceAccountId)
	}
	if update_type == match_api.BalanceUpdateType_BALANCE_UPDATE_TOTAL_AVAIL || update_type == match_api.BalanceUpdateType_BALANCE_UPDATE_AVAIL_ONLY {
		ba.AvailBalance = ba.AvailBalance.Sub(amt)
	}
	if update_type == match_api.BalanceUpdateType_BALANCE_UPDATE_TOTAL_AVAIL || update_type == match_api.BalanceUpdateType_BALANCE_UPDATE_TOTAL_ONLY {
		ba.TotalBalance = ba.TotalBalance.Sub(amt)
	}
	if !disable_send_balance_update {
		resp := new_balance_update()
		bu := resp.Response.(*match_api.Response_BalanceUpdate).BalanceUpdate
		bu.BalanceAccountId = ba.BalanceAccountId
		bu.AssetSymbol = ba.AssetSymbol
		bu.OrderId = order.OrderId
		bu.Ref = ref
		bu.UpdateType = update_type
		*bu.UpdateAmount = amt.ToProto()
		*bu.BalanceAvail = ba.AvailBalance.ToProto()
		*bu.BalanceTotal = ba.TotalBalance.ToProto()
		bu.MarketCode = ob.Market.MarketId
		bu.Scenario = scenario
		bu.ScenarioType = scenario_type
		bu.BalanceUpdateId = new_rand_balance_update_id(ob.Rand)
		bu.Time = uint64(time.Now().UnixNano()) // move to start of request for speed
		if msg != nil {
			msg.AddReport(resp)
		}
	}
	return nil
}*/

func send_price_level_update(params *PriceLevelUpdateParams) {
	log.Info().Msgf("send_price_level_update: params=%+v", params)
	if !disable_send_price_update {
		report := new_price_update()
		pu := report.Response.(*match_api.Response_PriceUpdate).PriceUpdate
		pu.Symbol = params.Symbol
		pu.Side = utils.SideToProto(params.Side)
		*pu.Price = params.Price.ToProto()
		*pu.Qty = params.NewQty.ToProto()
		*pu.OldQty = params.OldQty.ToProto()
		pu.Offset = params.Ob.next_price_offset
		pu.Time = params.Time
		pu.Depth = uint32(params.Depth)
		*pu.LastTradePrice = params.Ob.LastTradePrice.ToProto()
		pu.LastTradeTime = params.Ob.LastTradeTime
		*pu.LastTradeQty = params.Ob.LastTradeQty.ToProto()
		pu.Timezone = params.Ob.Market.Timezone.String()
		pu.TradingSessionId = params.Ob.Market.ActiveRoutine.StateName
		params.Ob.next_price_offset++
		pu.NumOrders = uint64(params.NumOrders)
		pu.MarketModel = params.Ob.Market.MarketModel
		log.Debug().Msgf("price_update: %+v", pu)
		if params.BondPricePct != "" {
			pu.BondPricePct = params.BondPricePct
		}
		/*update := &PriceLevelUpdate{
			Symbol: symbol,
			Side:   side,
			Price:  price,
			Qty:    new_qty,
			Depth:  depth,
			ctx:    ctx,
		}
		//fix_price_level_updates <- update
		//http_price_level_updates <- update
		report := &Report{
			ReportType:  RespPriceUpdate,
			//ReportId:    ob.next_report_id,
			PriceUpdate: *update,
			ctx:         ctx,
		}
		//ob.next_report_id++
		if msg!=nil {
			msg.AddReport(report)
		}*/
		if params.Msg != nil {
			params.Msg.AddReport(report)
		}
	}
}

func obMatchBuyOrder(ctx context.Context, order *Order, ob *OrderBook, depth int, allow_match bool, eq_price decimal.Decimal, msg *KafkaMsg) error {
	for {
		tradeTime := uint64(GetClusterTimeUnixNano())
		level, _ := ob.ask_levels.Min()
		if level == nil {
			break
		}
		if order.OrdType != enum.OrdType_MARKET && !order.Price.IsZero() && level.price.GreaterThan(order.Price) {
			break
		}
		other, _ := level.orders.Min()
		if other.OpenQty.IsZero() {
			log.Fatal().Msgf("open order qty is zero: order=%+v other=%+v", order, other)
		}
		if other.HoldingAccountId == order.HoldingAccountId && order.PreventSelfTrade {
			log.Error().Msgf("self trade not allowed: order=%+v other=%+v", order, other)
			old_qty := level.tot_qty
			level.time = tradeTime
			level.tot_qty = level.tot_qty.Sub(other.OpenQty)
			send_price_level_update(&PriceLevelUpdateParams{
				Ctx:          ctx,
				Symbol:       order.Symbol,
				Side:         enum.Side_SELL,
				Price:        level.price,
				NewQty:       level.tot_qty,
				OldQty:       old_qty,
				Depth:        depth,
				Time:         level.time,
				NumOrders:    level.orders.Len(),
				Ob:           ob,
				Msg:          msg,
				BondPricePct: level.bond_price_pct,
			})
			other.OpenQty = decimal.Zero
			other.OrdStatus = enum.OrdStatus_CANCELED
			other.CancelReason = match_api.CancelReason_CANCEL_DUE_TO_SELF_MATCH_PREVENTION
			send_order_report_canceled(ctx, other, ob, msg)
		} else {
			trade_qty := decimal.Min(order.OpenQty, other.OpenQty)
			trade_price := other.Price
			if !eq_price.IsZero() {
				trade_price = eq_price
			}
			trade_amt := trade_qty.Mul(trade_price)
			if order.OrdType == enum.OrdType_MARKET && order.CumTradeAmount.Add(trade_amt).GreaterThan(order.EarmarkAmount) {
				trade_amt = order.EarmarkAmount.Sub(order.CumTradeAmount)
				trade_qty = trade_amt.Div(trade_price)
			}
			trade_id := new_rand_trade_id()

			lastestBaseAccruedInterest := order.ContractAccrIntAmt
			lastPaidInterest := lastestBaseAccruedInterest.Mul(trade_qty)
			log.Debug().Msgf("new trade trade_id=%s trade_qty=%v other=%+v lastestBaseAccruedInterest %v lastPaidInterest %v", trade_id, trade_qty, other, lastestBaseAccruedInterest, lastPaidInterest)

			update_order_trade(ctx, ob, other, trade_id, trade_qty, trade_price, trade_amt, lastPaidInterest, false, msg)
			update_order_trade(ctx, ob, order, trade_id, trade_qty, trade_price, trade_amt, lastPaidInterest, true, msg)

			ob.LastTradeTime = tradeTime
			ob.LastTradePrice = trade_price
			ob.LastTradeQty = trade_qty
			if !other.OpenQty.IsZero() && !other.MinQty.IsZero() && other.OpenQty.LessThan(other.MinQty) {
				old_qty := level.tot_qty
				level.time = tradeTime
				level.tot_qty = level.tot_qty.Sub(trade_qty.Add(other.OpenQty))
				send_price_level_update(&PriceLevelUpdateParams{
					Ctx:          ctx,
					Symbol:       order.Symbol,
					Side:         enum.Side_SELL,
					Price:        level.price,
					NewQty:       level.tot_qty,
					OldQty:       old_qty,
					Depth:        depth,
					Time:         level.time,
					NumOrders:    level.orders.Len(),
					Ob:           ob,
					Msg:          msg,
					BondPricePct: level.bond_price_pct,
				})
				other.OpenQty = decimal.Zero
				other.OrdStatus = enum.OrdStatus_CANCELED
				other.CancelReason = match_api.CancelReason_CANCEL_DUE_TO_REMAINING_QTY_TOO_LOW
				send_order_report_canceled(ctx, other, ob, msg)
			} else {
				old_qty := level.tot_qty
				level.tot_qty = level.tot_qty.Sub(trade_qty)
				level.time = tradeTime
				send_price_level_update(&PriceLevelUpdateParams{
					Ctx:          ctx,
					Symbol:       order.Symbol,
					Side:         enum.Side_SELL,
					Price:        level.price,
					NewQty:       level.tot_qty,
					OldQty:       old_qty,
					Depth:        depth,
					Time:         level.time,
					NumOrders:    level.orders.Len(),
					Ob:           ob,
					Msg:          msg,
					BondPricePct: level.bond_price_pct,
				})
			}
			metrics.TradesCreated++
		}
		if other.OpenQty.IsZero() {
			level.orders.Delete(other)
			if level.orders.Len() == 0 {
				ob.ask_levels.Delete(level)
				free_price_level(level)
			}
			if ob.cl_open_orders[other.ClOrdId] == nil {
				log.Fatal().Msgf("open order %s not found", other.ClOrdId)
			}
			deleteOpenOrder(ob, other, other.ClOrdId)
			//log.Info().Msgf("-%s",other.ClOrdId)
			if other.TimerId != 0 {
				remove_expire_timer(other)
			}
			free_order(other)
		}
		if order.OpenQty.IsZero() {					
			break
		}
		if order.OrdType == enum.OrdType_MARKET && order.CumTradeAmount.GreaterThanOrEqual(order.EarmarkAmount) {
			break
		}
		depth += 1
	}
	return nil
}

func obMatchSellOrder(ctx context.Context, order *Order, ob *OrderBook, depth int, allow_match bool, eq_price decimal.Decimal, msg *KafkaMsg) error {
	for {
		level, _ := ob.bid_levels.Max()
		if level == nil {
			break
		}
		if order.OrdType != enum.OrdType_MARKET && !order.Price.IsZero() && level.price.LessThan(order.Price) {
			break
		}
		other, _ := level.orders.Min()
		if other.OpenQty.IsZero() {
			log.Fatal().Msgf("open order qty is zero: order=%+v other=%+v", order, other)
		}
		tradeTime := uint64(GetClusterTimeUnixNano())
		if other.HoldingAccountId == order.HoldingAccountId && order.PreventSelfTrade {
			log.Error().Msgf("self trade not allowed: order=%+v other=%+v", order, other)
			old_qty := level.tot_qty
			level.tot_qty = level.tot_qty.Sub(other.OpenQty)
			level.time = tradeTime
			send_price_level_update(&PriceLevelUpdateParams{
				Ctx:          ctx,
				Symbol:       order.Symbol,
				Side:         enum.Side_BUY,
				Price:        level.price,
				NewQty:       level.tot_qty,
				OldQty:       old_qty,
				Depth:        depth,
				Time:         level.time,
				NumOrders:    level.orders.Len(),
				Ob:           ob,
				Msg:          msg,
				BondPricePct: level.bond_price_pct,
			})
			other.OpenQty = decimal.Zero
			other.OrdStatus = enum.OrdStatus_CANCELED
			order.CancelReason = match_api.CancelReason_CANCEL_DUE_TO_SELF_MATCH_PREVENTION
			send_order_report_canceled(ctx, other, ob, msg)
		} else {
			trade_id := new_rand_trade_id()
			trade_qty := decimal.Min(order.OpenQty, other.OpenQty)
			trade_price := other.Price
			if !eq_price.IsZero() {
				trade_price = eq_price
			}
			trade_amt := trade_qty.Mul(trade_price)

			lastestBaseAccruedInterest := order.ContractAccrIntAmt
			lastPaidInterest := lastestBaseAccruedInterest.Mul(trade_qty)
			log.Debug().Msgf("new trade trade_id=%s trade_qty=%v other=%+v  lastPaidInterestAmt %v lastPaidInterest %v", trade_id, trade_qty, other, lastestBaseAccruedInterest, lastPaidInterest)

			update_order_trade(ctx, ob, other, trade_id, trade_qty, trade_price, trade_amt, lastPaidInterest, false, msg)
			update_order_trade(ctx, ob, order, trade_id, trade_qty, trade_price, trade_amt, lastPaidInterest, true, msg)
			ob.LastTradePrice = trade_price
			ob.LastTradeTime = tradeTime
			ob.LastTradeQty = trade_qty
			if !other.OpenQty.IsZero() && !other.MinQty.IsZero() && other.OpenQty.LessThan(other.MinQty) {
				old_qty := level.tot_qty
				level.tot_qty = level.tot_qty.Sub(trade_qty.Add(other.OpenQty))
				level.time = tradeTime
				send_price_level_update(&PriceLevelUpdateParams{
					Ctx:          ctx,
					Symbol:       order.Symbol,
					Side:         enum.Side_BUY,
					Price:        level.price,
					NewQty:       level.tot_qty,
					OldQty:       old_qty,
					Depth:        depth,
					Time:         level.time,
					NumOrders:    level.orders.Len(),
					Ob:           ob,
					Msg:          msg,
					BondPricePct: level.bond_price_pct,
				})

				other.OpenQty = decimal.Zero
				other.OrdStatus = enum.OrdStatus_CANCELED
				other.CancelReason = match_api.CancelReason_CANCEL_DUE_TO_REMAINING_QTY_TOO_LOW
				send_order_report_canceled(ctx, other, ob, msg)
			} else {
				old_qty := level.tot_qty
				level.tot_qty = level.tot_qty.Sub(trade_qty)
				level.time = tradeTime
				send_price_level_update(&PriceLevelUpdateParams{
					Ctx:          ctx,
					Symbol:       order.Symbol,
					Side:         enum.Side_BUY,
					Price:        level.price,
					NewQty:       level.tot_qty,
					OldQty:       old_qty,
					Depth:        depth,
					Time:         level.time,
					NumOrders:    level.orders.Len(),
					Ob:           ob,
					Msg:          msg,
					BondPricePct: level.bond_price_pct,
				})
			}
			metrics.TradesCreated++
		}
		if other.OpenQty.IsZero() {
			level.orders.Delete(other)
			if level.orders.Len() == 0 {
				ob.bid_levels.Delete(level)
				free_price_level(level)
			}
			if ob.cl_open_orders[other.ClOrdId] == nil {
				log.Fatal().Msgf("open order %s not found", other.ClOrdId)
			}
			deleteOpenOrder(ob, other, other.ClOrdId)
			//log.Info().Msgf("-%s",other.ClOrdId)
			if other.TimerId != 0 {
				remove_expire_timer(other)
			}
			free_order(other)
		}
		if order.OpenQty.IsZero() {					
			break
		}
		depth += 1
	}
	return nil
}

func checkRemainingOpenQtyBuyOrder(ctx context.Context, order *Order, ob *OrderBook, msg *KafkaMsg) {
	if order.TimeInForce == enum.TimeInForce_IMMEDIATE_OR_CANCEL {
		order.OrdStatus = enum.OrdStatus_CANCELED
		send_order_report_canceled(ctx, order, ob, msg)
		free_order(order)
	} else {
		t := uint64(GetClusterTimeUnixNano())
		new_level := new_price_level()
		new_level.price = order.Price
		level, _ := ob.bid_levels.Get(new_level)
		if level != nil {
			free_price_level(new_level)
		} else {
			level = new_level
			level.tot_qty = decimal.Zero
			ob.bid_levels.ReplaceOrInsert(level)
		}
		order.CreateTime = t
		level.orders.ReplaceOrInsert(order)
		old_qty := level.tot_qty
		level.tot_qty = old_qty.Add(order.OpenQty)
		level.time = t
		if order.BondPricePct != "" {
			level.bond_price_pct = order.BondPricePct
		}

		depth, _ := ob.bid_levels.GetReversePosition(level)
		log.Debug().Msgf("bid_levels.GetReversePosition: %d", depth)
		send_price_level_update(&PriceLevelUpdateParams{
			Ctx:          ctx,
			Symbol:       order.Symbol,
			Side:         enum.Side_BUY,
			Price:        level.price,
			NewQty:       level.tot_qty,
			OldQty:       old_qty,
			Depth:        depth,
			Time:         level.time,
			NumOrders:    level.orders.Len(),
			Ob:           ob,
			Msg:          msg,
			BondPricePct: level.bond_price_pct,
		})
		ob.cl_open_orders[order.ClOrdId] = order

		// move to the top of the function
		//if order.ExpireTime != 0 {
		//	add_expire_timer(order)
		//}
	}
}

func checkRemainingOpenQtySellOrder(ctx context.Context, order *Order, ob *OrderBook, msg *KafkaMsg) {
	if order.TimeInForce == enum.TimeInForce_IMMEDIATE_OR_CANCEL {
		order.OrdStatus = enum.OrdStatus_CANCELED
		send_order_report_canceled(ctx, order, ob, msg)
		free_order(order)
	} else {
		t := uint64(GetClusterTimeUnixNano())
		new_level := new_price_level()
		new_level.price = order.Price
		level, _ := ob.ask_levels.Get(new_level)
		if level != nil {
			free_price_level(new_level)
		} else {
			level = new_level
			level.tot_qty = decimal.Zero
			ob.ask_levels.ReplaceOrInsert(level)
		}
		order.CreateTime = t
		level.orders.ReplaceOrInsert(order)
		old_qty := level.tot_qty
		level.tot_qty = old_qty.Add(order.OpenQty)
		level.time = t
		if order.BondPricePct != "" {
			level.bond_price_pct = order.BondPricePct
		}

		depth, _ := ob.ask_levels.GetPosition(level)
		log.Debug().Msgf("ask_levels.GetPosition: %d", depth)
		send_price_level_update(&PriceLevelUpdateParams{
			Ctx:          ctx,
			Symbol:       order.Symbol,
			Side:         enum.Side_SELL,
			Price:        level.price,
			NewQty:       level.tot_qty,
			OldQty:       old_qty,
			Depth:        depth,
			Time:         level.time,
			NumOrders:    level.orders.Len(),
			Ob:           ob,
			Msg:          msg,
			BondPricePct: level.bond_price_pct,
		})
		ob.cl_open_orders[order.ClOrdId] = order
		log.Debug().Msgf("ob cl_open_order %+v, symbol=%+v", ob.cl_open_orders, order.Symbol)

		//move to the top
		//if order.ExpireTime != 0 {
		//	add_expire_timer(order)
		//}

	}
}


// Refer to TRADE_FEE_CALC.md
func ob_add_order(ctx context.Context, order *Order, ob *OrderBook, allow_match bool, eq_price decimal.Decimal, msg *KafkaMsg) error {
	log.Debug().Msgf("ob_add_order order=%+v", order)
	if order.Side == enum.Side_BUY {
		depth := 0
		if allow_match {
			// to add to expiry scheduler at first if failed then must to reject
			if order.ExpireTime != 0 {
				ok := add_expire_timer(order)
				if !ok {
					return fmt.Errorf("failed to register expiry order")
				}
			}

			obMatchBuyOrder(ctx, order, ob, depth, allow_match, eq_price, msg)
		}
		
		if !order.OpenQty.IsZero() {
			checkRemainingOpenQtyBuyOrder(ctx, order, ob, msg)		
		} else {
			if ob.cl_open_orders[order.ClOrdId] != nil {
				deleteOpenOrder(ob, order, order.ClOrdId)
			}
			if order.ExpireTime != 0 {
				remove_expire_timer(order)
			}
			free_order(order)
		}

		if !order.Price.IsZero() {
			ob.LastBid = order.Price
		}

	} else {
		depth := 0
		if allow_match {
			// to add to expiry scheduler at first if failed then must to reject
			if order.ExpireTime != 0 {
				ok := add_expire_timer(order)
				if !ok {
					return fmt.Errorf("failed to register expiry order")
				}
			}
			
			obMatchSellOrder(ctx, order, ob, depth, allow_match, eq_price, msg)
		}


		if !order.OpenQty.IsZero() {
			checkRemainingOpenQtySellOrder(ctx, order, ob, msg)
		} else {
			if ob.cl_open_orders[order.ClOrdId] != nil {
				deleteOpenOrder(ob, order, order.ClOrdId)
			}
			if order.ExpireTime != 0 {
				remove_expire_timer(order)
			}
			free_order(order)
		}
		if !order.Price.IsZero() {
			ob.LastAsk = order.Price
		}
	}
	level, _ := ob.ask_levels.Min()
	if level != nil {
		ob.BestAsk = level.price
	} else {
		ob.BestAsk = decimal.Zero
	}
	level, _ = ob.bid_levels.Max()
	if level != nil {
		ob.BestBid = level.price
	} else {
		ob.BestBid = decimal.Zero
	}
	return nil
}

func add_expire_timer(order *Order) bool {
	timer_count++
	order.TimerId = timer_count
	timer_events[order.TimerId] = order
	ok := aeron_schedule_timer(order.TimerId, int64(order.ExpireTime))
	log.Debug().Msgf("add_expire_timer to aeron_schedule_timer cli_order_id=%s timer_count=%d expiry_unix_milli=%d ok=%t",
		order.ClOrdId, timer_count, order.ExpireTime, ok)
	return ok
}

func remove_expire_timer(order *Order) {
	delete(timer_events, order.TimerId)
}

var orderPool = sync.Pool{
	New: func() interface{} {
		return &Order{}
	},
}

func new_order() *Order {
	metrics.AllocOrders++
	order := orderPool.Get().(*Order)
	*order = Order{} // clear data
	//return orderPool.Get().(*Order)
	return order
}

func free_order(order *Order) {
	metrics.AllocOrders--
	orderPool.Put(order)
}

var priceLevelPool = sync.Pool{
	New: func() interface{} {
		level := &PriceLevel{}
		level.orders = btree.NewG[*Order](2, func(a, b *Order) bool { return a.CreateTime < b.CreateTime })
		return level
	},
}

func new_price_level() *PriceLevel {
	metrics.AllocPriceLevels++
	return priceLevelPool.Get().(*PriceLevel)
}

func free_price_level(level *PriceLevel) {
	metrics.AllocPriceLevels--
	priceLevelPool.Put(level)
}

func new_rand_trade_id() string {
	// 18-Nov-2024. change to use sequence instead!
	//n := match_rand.Int63n(100_000_000_000_000)
	//return fmt.Sprintf("T-%014d", n)
	return NewTradeID()
}

func new_rand_exec_id() string {
	// 18-Nov-2024. change to use sequence instead!
	//n := match_rand.Int63n(100_000_000_000_000)
	//return fmt.Sprintf("E-%014d", n)
	return NewExecID()
}

func get_buy_fill(ob *OrderBook, qty decimal.Decimal) (fill_qty, fill_amount decimal.Decimal) {
	fill_qty = decimal.Zero
	fill_amount = decimal.Zero
	ob.ask_levels.Ascend(func(level *PriceLevel) bool {
		if level.tot_qty.LessThanOrEqual(qty) {
			fill_qty = fill_qty.Add(level.tot_qty)
			fill_amount = fill_amount.Add(level.tot_qty.Mul(level.price))
			qty = qty.Sub(level.tot_qty)
		} else {
			fill_qty = fill_qty.Add(qty)
			fill_amount = fill_amount.Add(qty.Mul(level.price))
			qty = decimal.Zero
		}
		if qty.IsZero() {
			return false
		}
		return true
	})
	return fill_qty, fill_amount
}

func get_sell_fill(ob *OrderBook, qty decimal.Decimal) (fill_qty, fill_amount decimal.Decimal) {
	fill_qty = decimal.Zero
	fill_amount = decimal.Zero
	ob.bid_levels.Descend(func(level *PriceLevel) bool {
		if level.tot_qty.LessThanOrEqual(qty) {
			fill_qty = fill_qty.Add(level.tot_qty)
			fill_amount = fill_amount.Add(level.tot_qty.Mul(level.price))
			qty = qty.Sub(level.tot_qty)
		} else {
			fill_qty = fill_qty.Add(qty)
			fill_amount = fill_amount.Add(qty.Mul(level.price))
			qty = decimal.Zero
		}
		if qty.IsZero() {
			return false
		}
		return true
	})
	return fill_qty, fill_amount
}

func process_new_order(order *Order, msg *KafkaMsg, hx_order_fee_details map[string]*match_api.UDec128) error {
	ctx := order.Ctx
	if tracer != nil {
		ctx2, span := tracer.Start(ctx, "process_new_order")
		ctx = ctx2
		defer span.End()
	}
	//req.ctx = ctx

	ob := order.Orderbook

	/*order := &Order{
		OrderId:      ob.next_order_id,
		ClOrdId:      req.ClOrdId,
		Symbol:       req.Symbol,
		Side:         req.Side,
		OrdType:      req.OrdType,
		Price:        req.Price,
		Qty:          req.Qty,
		OpenQty:      req.Qty,
		TargetCompId: req.TargetCompId,
		OrdStatus:    enum.OrdStatus_NEW,
	}*/
	//order:=&Order{}
	order.OrdStatus = enum.OrdStatus_NEW
	order.OpenQty = order.Qty
	order.CumTradeQty = decimal.Zero
	order.CumTradeAmount = decimal.Zero
	order.CumFeeAmount = decimal.Zero
	order.CumTaxAmount = decimal.Zero

	if ob.cl_open_orders[order.ClOrdId] != nil {
		log.Debug().Msgf("Duplicated Order: cl_ord_id=%s cl_open_orders=%+v", order.ClOrdId, ob.cl_open_orders)
		return fmt.Errorf("Duplicated Order: %s", order.ClOrdId)
	}

	/*ha := order.HoldingAccount
	if ha.AccountType == "FAST_TRADING_ACCOUNT" {
		err := check_order_balance_local(order, msg)
		if err != nil {
			return err
		}
	}*/

	//fmt.Printf("Order %+v\n",order)
	send_order_report_new(ctx, order, ob, msg, hx_order_fee_details)

	if order.TimeInForce == enum.TimeInForce_FILL_OR_KILL {
		if !canFillOrKillOrder(order, ob) {
			order.OpenQty = decimal.Zero
			order.OrdStatus = enum.OrdStatus_CANCELED
			order.CancelReason = match_api.CancelReason_CANCEL_DUE_TO_REMAINING_QTY_TOO_LOW
			send_order_report_canceled(ctx, order, ob, msg)
			free_order(order)
			return nil
		}

	}
	allowMatch := IsRoutineAllowMatch(order.Symbol)
	log.Info().Msgf("host=%s symbol=%s side=%s process_new_order with can_math=%t", HOST_NAME, order.Symbol, order.Side, allowMatch)
	ob_add_order(ctx, order, ob, allowMatch, decimal.Zero, msg)

	return nil
}

// func canFillOrKillOrder(order *Order, ob *OrderBook) bool {
// 	requiredQty := order.Qty
// 	matchedQty := decimal.Zero
// 	matchedAmt := decimal.Zero 

// 	if order.Side == enum.Side_BUY {
// 		canFillOrKillBuyOrder(order, ob, requiredQty, &matchedQty, &matchedAmt)
// 	} else {
// 		canFillOrKillSellOrder(order, ob, requiredQty, &matchedQty, &matchedAmt)
// 	}

// 	return matchedQty.GreaterThanOrEqual(requiredQty)
// }

// func canFillOrKillBuyOrder(order *Order, ob *OrderBook, requiredQty decimal.Decimal, matchedQty *decimal.Decimal, matchedAmt *decimal.Decimal) {
// 	ob.ask_levels.Ascend(func(level *PriceLevel) bool {
// 		if order.OrdType != enum.OrdType_MARKET && level.price.GreaterThan(order.Price) {
// 			return false
// 		}		
// 		level.orders.Ascend(func(o *Order) bool {
// 			if o.OpenQty.IsZero() || (order.PreventSelfTrade && o.HoldingAccountId == order.HoldingAccountId) {
// 				return true
// 			}
			
// 			remainingQty := requiredQty.Sub(*matchedQty)			
// 			qty := decimal.Min(o.OpenQty, remainingQty)
// 			tradeAmt := qty.Mul(o.Price)

// 			if order.OrdType == enum.OrdType_MARKET && !order.EarmarkAmount.IsZero() {
// 				remainingAmt := order.EarmarkAmount.Sub(*matchedAmt)
// 				if tradeAmt.GreaterThan(remainingAmt) {
// 					qty = remainingAmt.Div(o.Price)
// 					tradeAmt = qty.Mul(o.Price)
// 				}
// 				*matchedAmt = matchedAmt.Add(tradeAmt)
// 			}

// 			*matchedQty = matchedQty.Add(qty)
// 			return matchedQty.LessThan(requiredQty)
// 		})
// 		return matchedQty.LessThan(requiredQty)
// 	})
// }

// func canFillOrKillSellOrder(order *Order, ob *OrderBook, requiredQty decimal.Decimal, matchedQty *decimal.Decimal, matchedAmt *decimal.Decimal) {
// 	ob.bid_levels.Descend(func(level *PriceLevel) bool {
// 		if order.OrdType != enum.OrdType_MARKET && level.price.LessThan(order.Price) {
// 			return false
// 		}
// 		level.orders.Ascend(func(o *Order) bool {
// 			if o.OpenQty.IsZero() || (o.HoldingAccountId == order.HoldingAccountId && order.PreventSelfTrade)  {
// 				return true
// 			}

// 			remainingQty := requiredQty.Sub(*matchedQty)
// 			qty := decimal.Min(o.OpenQty, remainingQty)
// 			tradeAmt := qty.Mul(o.Price)

// 			if order.OrdType == enum.OrdType_MARKET && !order.EarmarkAmount.IsZero() {
// 				remainingAmt := order.EarmarkAmount.Sub(*matchedAmt)
// 				if tradeAmt.GreaterThan(remainingAmt) {
// 					qty = remainingAmt.Div(o.Price)
// 					tradeAmt = qty.Mul(o.Price)
// 				}
// 				*matchedAmt = matchedAmt.Add(tradeAmt)
// 			}
// 			*matchedQty = matchedQty.Add(qty)

// 			return matchedQty.LessThan(requiredQty)
// 		})
// 		return matchedQty.LessThan(requiredQty)
// 	})
// }

func canFillOrKillOrder(order *Order, ob *OrderBook) bool {
	requiredQty := order.Qty
	matchedQty := decimal.Zero
	matchedAmt := decimal.Zero

	var iterateLevels func(func(*PriceLevel) bool)
	var priceFilter func(levelPrice, orderPrice decimal.Decimal) bool

	if order.Side == enum.Side_BUY {
		iterateLevels = func(fn func(*PriceLevel) bool) {
			ob.ask_levels.Ascend(fn)
		}
		priceFilter = func(levelPrice, orderPrice decimal.Decimal) bool {
			return order.OrdType == enum.OrdType_MARKET || !levelPrice.GreaterThan(order.Price)
		}
	} else {
		iterateLevels = func(fn func(*PriceLevel) bool) {
			ob.bid_levels.Descend(fn)
		}
		priceFilter = func(levelPrice, orderPrice decimal.Decimal) bool {
			return order.OrdType == enum.OrdType_MARKET || !levelPrice.LessThan(order.Price)
		}
	}

	processLevels(order, requiredQty, &matchedQty, &matchedAmt, iterateLevels, priceFilter)
	if order.OrdType == enum.OrdType_MARKET && order.Side == enum.Side_BUY && !order.EarmarkAmount.IsZero() {
		return matchedQty.GreaterThanOrEqual(requiredQty) && matchedAmt.LessThanOrEqual(order.EarmarkAmount)
	}
	return matchedQty.GreaterThanOrEqual(requiredQty)
}

func processLevels(
	order *Order,
	requiredQty decimal.Decimal,
	matchedQty *decimal.Decimal,
	matchedAmt *decimal.Decimal,
	iterateLevels func(func(*PriceLevel) bool),
	priceFilter func(levelPrice, orderPrice decimal.Decimal) bool,
) {
	iterateLevels(func(level *PriceLevel) bool {
		if !priceFilter(level.price, order.Price) {
			return false
		}

		level.orders.Ascend(func(o *Order) bool {
			if o.OpenQty.IsZero() || (order.PreventSelfTrade && o.HoldingAccountId == order.HoldingAccountId) {
				return true
			}

			remainingQty := requiredQty.Sub(*matchedQty)
			qty := decimal.Min(o.OpenQty, remainingQty)
			tradeAmt := qty.Mul(o.Price)

			if order.OrdType == enum.OrdType_MARKET && order.Side == enum.Side_BUY && !order.EarmarkAmount.IsZero() {
				remainingAmt := order.EarmarkAmount.Sub(*matchedAmt)
				if tradeAmt.GreaterThan(remainingAmt) {
					qty = remainingAmt.Div(o.Price)
					tradeAmt = qty.Mul(o.Price)
				}
				*matchedAmt = matchedAmt.Add(tradeAmt)
			}

			*matchedQty = matchedQty.Add(qty)
			return matchedQty.LessThan(requiredQty)
		})

		return matchedQty.LessThan(requiredQty)
	})
}

func process_cancel_order(order *Order, msg *KafkaMsg) error {
	//fmt.Printf("process_cancel_order %s\n", order.Symbol)
	ctx := order.Ctx
	//ctx, span := tracer.Start(order.ctx, "match_process_cancel_order")
	//defer span.End()
	//order.ctx = ctx
	now := uint64(GetClusterTimeUnixNano())

	ob := order.Orderbook
	orig_order := ob.cl_open_orders[order.OrigClOrdId]
	if orig_order == nil {
		log.Error().Msgf("Unknown order %s", order.OrigClOrdId)
		return fmt.Errorf("order not found in open orders")
	}
	if orig_order.Side == enum.Side_BUY {
		level, _ := ob.bid_levels.Get(&PriceLevel{price: orig_order.Price})
		if level == nil {
			return fmt.Errorf("Unknown price level %s", orig_order.Price.String())
		}
		level.orders.Delete(orig_order)
		old_qty := level.tot_qty
		level.tot_qty = old_qty.Sub(orig_order.OpenQty)
		level.time = now
		send_price_level_update(&PriceLevelUpdateParams{
			Ctx:          ctx,
			Symbol:       orig_order.Symbol,
			Side:         enum.Side_BUY,
			Price:        level.price,
			NewQty:       level.tot_qty,
			OldQty:       old_qty,
			Depth:        0,
			Time:         level.time,
			NumOrders:    level.orders.Len(),
			Ob:           ob,
			Msg:          msg,
			BondPricePct: level.bond_price_pct,
		})
		if level.orders.Len() == 0 {
			ob.bid_levels.Delete(level)
			free_price_level(level)
		}
	} else {
		level, _ := ob.ask_levels.Get(&PriceLevel{price: orig_order.Price})
		if level == nil {
			return fmt.Errorf("Unknown price level %s", orig_order.Price.String())
		}
		level.orders.Delete(orig_order)
		old_qty := level.tot_qty
		level.tot_qty = old_qty.Sub(orig_order.OpenQty)
		level.time = now
		send_price_level_update(&PriceLevelUpdateParams{
			Ctx:          ctx,
			Symbol:       orig_order.Symbol,
			Side:         enum.Side_SELL,
			Price:        level.price,
			NewQty:       level.tot_qty,
			OldQty:       old_qty,
			Depth:        0,
			Time:         level.time,
			NumOrders:    level.orders.Len(),
			Ob:           ob,
			Msg:          msg,
			BondPricePct: level.bond_price_pct,
		})
		if level.orders.Len() == 0 {
			ob.ask_levels.Delete(level)
			free_price_level(level)
		}
	}
	if orig_order.OrdStatus == enum.OrdStatus_CANCELED {
		// impossible to happen
		log.Fatal().Msgf("Order %s was already canceled", order.OrigClOrdId)
	}
	if orig_order.OrdStatus == enum.OrdStatus_FILLED {
		// impossible to happen
		log.Fatal().Msgf("Order %s was already filled", order.OrigClOrdId)
	}

	orig_order.OpenQty = decimal.Zero
	orig_order.OrdStatus = enum.OrdStatus_CANCELED
	orig_order.CancelReason = order.CancelReason
	send_order_report_canceled(ctx, orig_order, ob, msg) // XXX
	deleteOpenOrder(ob, orig_order, order.OrigClOrdId)
	free_order(order)
	free_order(orig_order)

	return nil
}

func process_edit_order(order *Order, msg *KafkaMsg) error {
	//fmt.Printf("process_edit_order %s\n", order.Symbol)
	ctx := order.Ctx
	//ctx, span := tracer.Start(order.ctx, "match_process_edit_order")
	//defer span.End()
	//order.ctx = ctx
	now := uint64(GetClusterTimeUnixNano())

	ob := order.Orderbook
	orig_order := ob.cl_open_orders[order.OrigClOrdId]
	if orig_order == nil {
		return fmt.Errorf("Unknown order %s", order.OrigClOrdId)
	}
	if orig_order.Side == enum.Side_BUY {
		level, _ := ob.bid_levels.Get(&PriceLevel{price: orig_order.Price})
		if level == nil {
			return fmt.Errorf("Unknown price level %s", orig_order.Price.String())
		}
		level.orders.Delete(orig_order)
		old_qty := level.tot_qty
		level.tot_qty = old_qty.Sub(orig_order.OpenQty)
		level.time = now
		if !orig_order.Price.Equal(order.Price) {
			send_price_level_update(&PriceLevelUpdateParams{
				Ctx:          ctx,
				Symbol:       order.Symbol,
				Side:         enum.Side_BUY,
				Price:        level.price,
				NewQty:       level.tot_qty,
				OldQty:       old_qty,
				Depth:        0,
				Time:         level.time,
				NumOrders:    level.orders.Len(),
				Ob:           ob,
				Msg:          msg,
				BondPricePct: level.bond_price_pct,
			})
		}
		if level.orders.Len() == 0 {
			ob.bid_levels.Delete(level)
			free_price_level(level)
		}
	} else {
		level, _ := ob.ask_levels.Get(&PriceLevel{price: orig_order.Price})
		if level == nil {
			return fmt.Errorf("Unknown price level %s", orig_order.Price.String())
		}
		level.orders.Delete(orig_order)
		old_qty := level.tot_qty
		level.tot_qty = old_qty.Sub(orig_order.OpenQty)
		level.time = now
		if !orig_order.Price.Equal(order.Price) {
			send_price_level_update(&PriceLevelUpdateParams{
				Ctx:          ctx,
				Symbol:       order.Symbol,
				Side:         enum.Side_BUY,
				Price:        level.price,
				NewQty:       level.tot_qty,
				OldQty:       old_qty,
				Depth:        0,
				Time:         level.time,
				NumOrders:    level.orders.Len(),
				Ob:           ob,
				Msg:          msg,
				BondPricePct: level.bond_price_pct,
			})
		}
		if level.orders.Len() == 0 {
			ob.ask_levels.Delete(level)
			free_price_level(level)
		}
	}

	mkt := ob.Market
	routine := mkt.ActiveRoutine

	//fmt.Printf("Order %+v\n",order)
	order.OrdStatus = enum.OrdStatus_NEW
	send_order_report_new(ctx, order, ob, msg, nil)
	ob_add_order(ctx, order, ob, routine.AllowMatch, decimal.Zero, msg)

	orig_order.OpenQty = decimal.Zero
	orig_order.OrdStatus = enum.OrdStatus_REPLACED
	send_order_report_replaced(ctx, orig_order, ob, msg)

	return nil
}

func filterOrderCOD(req *match_api.DisconnectReq, order *Order) bool {
	log.Debug().Msgf("process_disconnect order=%+v", order)
	user, _ := users[order.UserId]
	if user == nil {
		log.Debug().Msgf("Skip User not a fix user: %s", order.UserId)
		return false
	}
	if !user.CodEnabled {
		log.Debug().Msgf("COD not enabled: %s", order.UserId)
		return false
	}
	if req.DisconnectType == match_api.DisconnectType_DISCONNECT_TYPE_FIX_SESSION {
		if order.SenderCompId != req.SenderCompId {
			log.Debug().Msgf("SenderCompId not match: %s != %s", order.SenderCompId, req.SenderCompId)
			return false
		}
		if order.GatewayName != req.GatewayName {
			log.Debug().Msgf("GatewayName not match: %s != %s", order.GatewayName, req.GatewayName)
			return false
		}
	} else if req.DisconnectType == match_api.DisconnectType_DISCONNECT_TYPE_FIX_GATEWAY {
		if order.GatewayName != req.GatewayName {
			log.Debug().Msgf("GatewayName not match: %s != %s", order.GatewayName, req.GatewayName)
			return false
		}
	} else if req.DisconnectType == match_api.DisconnectType_DISCONNECT_TYPE_ORDER_VALIDATOR {
		if order.OrderValidatorName != req.OrderValidatorName {
			log.Debug().Msgf("OrderValidatorName not match: %s != %s", order.OrderValidatorName, req.OrderValidatorName)
			return false
		}
	} else if req.DisconnectType == match_api.DisconnectType_DISCONNECT_TYPE_MATCH_ENGINE {
	}

	return true
}


func sendCODPriceLevelUpdate(order *Order, ob *OrderBook, clusterTime uint64, ctx context.Context, msg *KafkaMsg) {
	if order.Side == enum.Side_BUY {
		level, _ := ob.bid_levels.Get(&PriceLevel{price: order.Price})
		if level == nil {
			log.Fatal().Msgf("Unknown price level %s", order.Price.String())
		}
		level.orders.Delete(order)
		old_qty := level.tot_qty
		level.tot_qty = old_qty.Sub(order.OpenQty)
		level.time = clusterTime
		send_price_level_update(&PriceLevelUpdateParams{
			Ctx:          ctx,
			Symbol:       order.Symbol,
			Side:         enum.Side_BUY,
			Price:        level.price,
			NewQty:       level.tot_qty,
			OldQty:       old_qty,
			Depth:        0,
			Time:         level.time,
			NumOrders:    level.orders.Len(),
			Ob:           ob,
			Msg:          msg,
			BondPricePct: level.bond_price_pct,
		})
		if level.orders.Len() == 0 {
			ob.bid_levels.Delete(level)
			free_price_level(level)
		}
	} else {
		level, _ := ob.ask_levels.Get(&PriceLevel{price: order.Price})
		if level == nil {
			log.Fatal().Msgf("Unknown price level %s", order.Price.String())
		}
		level.orders.Delete(order)
		old_qty := level.tot_qty
		level.tot_qty = old_qty.Sub(order.OpenQty)
		level.time = clusterTime
		send_price_level_update(&PriceLevelUpdateParams{
			Ctx:          ctx,
			Symbol:       order.Symbol,
			Side:         enum.Side_SELL,
			Price:        level.price,
			NewQty:       level.tot_qty,
			OldQty:       old_qty,
			Depth:        0,
			Time:         level.time,
			NumOrders:    level.orders.Len(),
			Ob:           ob,
			Msg:          msg,
			BondPricePct: level.bond_price_pct,
		})
		if level.orders.Len() == 0 {
			ob.ask_levels.Delete(level)
			free_price_level(level)
		}
	}
}

func process_disconnect(req *match_api.DisconnectReq, req_ *match_api.Request) {
	log.Debug().Msgf("process_disconnect req=%+v", req)
	ctx := context.Background()
	now := uint64(GetClusterTimeUnixNano())
	for _, ob := range orderbooks {
		for _, order := range ob.cl_open_orders {
			/*if !order.CancelOnDisconnect {
				continue
			}*/
			if !filterOrderCOD(req, order) {
				continue
			}

			var msg *KafkaMsg // XXX: remove this later
			msg = kafka_new_msg()

			sendCODPriceLevelUpdate(order, ob, now, ctx, msg)			
			order.OpenQty = decimal.Zero
			order.OrdStatus = enum.OrdStatus_CANCELED
			order.CancelReason = match_api.CancelReason_CANCEL_DUE_TO_CANCEL_ON_DISCONNECT
			send_order_report_canceled(ctx, order, ob, msg) // XXX
			deleteOpenOrder(ob, order, order.ClOrdId)

			msg.resp_list.Responses = msg.responses[:msg.num_responses]
			resp_list := msg.resp_list
			resp_list.SenderCompId = order.SenderCompId
			resp_list.RequestSentTime = req_.RequestSentTime
			resp_list.RequestReceivedTime = req_.RequestReceivedTime
			resp_list.RequestId = req_.RequestId
			resp_list.ResponseSentTime = now

			if !disable_send_match_out {
				aeron_match_out_send(resp_list, "process_disconnect")
			}
			if !disable_send_ov_in {
				aeron_client_ov_send(resp_list)
			}

			for _, resp := range resp_list.Responses {
				switch m := resp.Response.(type) {
				case *match_api.Response_ExecReport:
					free_exec_report(resp)
					metrics.ExecReportsSent++
					log.Debug().Msgf("sent exec_report: %+v", m)
				case *match_api.Response_PriceUpdate:
					free_price_update(resp)
					metrics.PriceUpdatesSent++
					log.Debug().Msgf("sent price_update: %+v", m)
				}
			}

			free_msg(msg)
		}
	}
}

func process_expire_order(order *Order) error {
	log.Debug().Msgf("process_expire_order symbol=%s, cli_order_id=%s", order.Symbol, order.ClOrdId)

	ctx := context.Background()
	//ctx, span := tracer.Start(context.Background(), "expire_order")
	//defer span.End()
	now := uint64(GetClusterTimeUnixNano())

	ob := order.Orderbook
	msg := kafka_new_msg()
	msg.Source = order.Source // XXX: remove?
	msg.RequestId = order.RequestId
	msg.Ctx = order.Ctx
	
	level, ok := getOrderbookLevel(order)
	if !ok {
		return fmt.Errorf("Unknown price level %s", order.Price.String())
	}
	_, ok = level.orders.Delete(order)
	if !ok {
		return fmt.Errorf("order not found from level %s, %v", order.Price.String(), order)
	}
	old_qty := level.tot_qty
	level.tot_qty = old_qty.Sub(order.OpenQty)
	level.time = now
	send_price_level_update(&PriceLevelUpdateParams{
		Ctx:          ctx,
		Symbol:       order.Symbol,
		Side:         order.Side,
		Price:        level.price,
		NewQty:       level.tot_qty,
		OldQty:       old_qty,
		Depth:        0,
		Time:         level.time,
		NumOrders:    level.orders.Len(),
		Ob:           ob,
		Msg:          msg,
		BondPricePct: level.bond_price_pct,
	})
	if level.orders.Len() == 0 {
		deleteOrderbookLevel(ob, order.Side, level)
	}

	order.OpenQty = decimal.Zero
	order.OrdStatus = enum.OrdStatus_CANCELED
	order.CancelReason = match_api.CancelReason_CANCEL_DUE_TO_EXPIRED_ORDER
	send_order_report_expired(ctx, order, ob, msg)
	deleteOpenOrder(ob, order, order.ClOrdId)

	msg.resp_list.Responses = msg.responses[:msg.num_responses]
	resp_list := msg.resp_list
	resp_list.SenderCompId = order.SenderCompId
	resp_list.RequestSentTime = now
	resp_list.RequestReceivedTime = now
	resp_list.RequestId = order.RequestId

	if !disable_send_match_out {
		aeron_match_out_send(resp_list, "process_expire_order")
	}
	if !disable_send_ov_in {
		aeron_client_ov_send(resp_list)
	}

	for _, resp := range resp_list.Responses {
		switch m := resp.Response.(type) {
		case *match_api.Response_ExecReport:
			free_exec_report(resp)
			metrics.ExecReportsSent++
			log.Debug().Msgf("sent exec_report: %+v", m)
		case *match_api.Response_PriceUpdate:
			free_price_update(resp)
			metrics.PriceUpdatesSent++
			log.Debug().Msgf("sent price_update: %+v", m)
		}
	}

	return nil
}

func getOrderbookLevel(order *Order) (*PriceLevel, bool){
	if order.Side == enum.Side_BUY {
		return order.Orderbook.bid_levels.Get(&PriceLevel{price: order.Price})
	}
	return order.Orderbook.ask_levels.Get(&PriceLevel{price: order.Price})
}

func deleteOrderbookLevel(ob *OrderBook, side enum.Side, level *PriceLevel) {
	if side == enum.Side_BUY {
		ob.bid_levels.Delete(level)
	} else {
		ob.ask_levels.Delete(level)
	}
	free_price_level(level)
}

func isNoRequiredUpdateDb(order *Order, ob *OrderBook) bool  {
	noRequiredUpdateDb := false
	if ob != nil {
		if _, ok := ob.cl_open_orders[order.ClOrdId]; ok {
			noRequiredUpdateDb = true
		}
		if _, ok := ob.cl_open_orders[order.OrigClOrdId]; ok {
			noRequiredUpdateDb = true
		}
	}
	return noRequiredUpdateDb
}

// When ME Reject for those non new order don't need to update database
// refer hx_writer_td_order on db_writer
func rejectOrder(order *Order, reason string, msg *KafkaMsg, ob *OrderBook) {
	log.Error().Msgf("Rejecting order %s: %s", order.OrderId, reason)
	ctx := order.Ctx
	if tracer != nil {
		ctx2, span := tracer.Start(ctx, "reject_order")
		ctx = ctx2
		defer span.End()
	}
	order.OrdStatus = enum.OrdStatus_REJECTED
	side := utils.SideToProto(order.Side)
	ord_status := utils.OrdStatusToProto(order.OrdStatus)

	if !disable_send_exec_report {
		report := new_exec_report()
		exec := report.Response.(*match_api.Response_ExecReport).ExecReport
		exec.ExecId = new_rand_exec_id()
		exec.ExecType = match_api.ExecType_EXEC_TYPE_REJECTED
		exec.NoRequiredDbUpdate = isNoRequiredUpdateDb(order, ob)
		exec.Symbol = order.Symbol
		exec.OrderId = order.OrderId
		exec.ClOrdId = order.ClOrdId
		*exec.Qty = order.Qty.ToProto()
		*exec.Price = order.Price.ToProto()
		*exec.EarmarkAmt = order.EarmarkAmount.ToProto()
		*exec.EarmarkFeeAmt = order.EarmarkFee.ToProto()
		*exec.EarmarkTaxAmt = order.EarmarkTax.ToProto()
		exec.Side = side
		exec.OrdStatus = ord_status
		*exec.OpenQty = order.OpenQty.ToProto()
		*exec.FillQty = order.CumTradeQty.ToProto()
		exec.SenderCompId = order.SenderCompId
		exec.HoldingAccountId = order.HoldingAccountId
		exec.ServiceAccountId = order.ServiceAccountId
		exec.BaseBalanceAccountId = order.BaseBalanceAccountId
		exec.QuoteBalanceAccountId = order.QuoteBalanceAccountId
		exec.UserId = order.UserId
		exec.TransactTime = uint64(GetClusterTimeUnixNano())
		exec.Text = reason
		exec.OrdType = utils.OrdTypeToProto(order.OrdType)
		exec.TimeInForce = utils.TimeInForceToProto(order.TimeInForce)
		exec.CreateTime = order.CreateTime
		exec.BondPricePct = order.BondPricePct
		exec.OperatorCode = order.OperatorCode
		if !order.BondNominalAmount.IsZero() {
			exec.BondNominalAmount = order.BondNominalAmount.ToProtoAlloc()
		}
		if msg != nil {
			msg.Source = order.Source
			msg.RequestId = order.RequestId
			msg.AddReport(report)
		}
	}

	free_order(order)
}

func validate_order(order *Order) error {
	log.Debug().Msgf("validate_order: %+v", order)

	ob := orderbooks[order.Symbol]
	if ob == nil {
		return fmt.Errorf("symbol not found, symbol: %s", order.Symbol)
	}
	order.Orderbook = ob

	order.PreventSelfTrade = !self_trade_allowed(order.HoldingAccountId, order.MarketCode)

	mkt := ob.Market
	order.MarketCode = mkt.Code
	order.OperatorCode = mkt.OperatorCode
	// set expiry time for DAY order
	if order.TimeInForce == enum.TimeInForce_DAY {
		//order.ExpireTime = uint64(utils.ToExpiryTime(order.TimeInForce, mkt.Timezone, mkt.OrderExpiryTime, nil))
		if mkt.DayOrderUTCExpiryTime > 0 && GetClusterTime().UnixMilli() > mkt.DayOrderUTCExpiryTime {
			mkt.DayOrderUTCExpiryTime = utils.ToExpiryTime(
				enum.TimeInForce_DAY, 
				mkt.Timezone, 
				mkt.OrderExpiryTime, 
				nil,
			)
		}
		order.ExpireTime = uint64(mkt.DayOrderUTCExpiryTime)
	}

	if disable_validate_order {
		return nil
	}


	return validateMktRoutine(order, mkt)
}

func process_order(req *match_api.Order, req_ *match_api.Request, session cluster.ClientSession) {
	log.Debug().Msgf("--- start process_order order=%s ---", req.ClOrdId)
	var span trace.Span
	if tracer != nil {
		if req.TraceId != "" {
			traceID, _ := trace.TraceIDFromHex(req.TraceId)
			sc := trace.NewSpanContext(trace.SpanContextConfig{
				TraceID:    traceID,
				SpanID:     trace.SpanID{}, // Generate new or use valid span ID
				TraceFlags: 0x1,            // Set the sampled flag
				Remote:     false,
			})
			_, span = tracer.Start(trace.ContextWithSpanContext(context.Background(), sc), "process_order")
			defer span.End()
		}
	}
	if span != nil {
		span.SetAttributes(attribute.String("cl_ord_id", req.ClOrdId))
	}

	if match_delay != 0 {
		log.Info().Msgf("Adding delay of %v ms", match_delay)
		time.Sleep(time.Duration(match_delay) * time.Millisecond)
	}

	order := new_order()
	order.CancelOrderIgnoreRoutineCheck = req.CancelOrderIgnoreRoutineCheck
	order.OrderId = req.OrderId
	order.RequestType = req.RequestType
	order.ClOrdId = req.ClOrdId
	order.Symbol = req.Symbol
	order.MarketCode = req.MarketCode
	order.Side, _ = utils.ProtoToSide(req.Side)
	order.OrdType, _ = utils.ProtoToOrdType(req.OrdType)
	order.Price = decimal.Zero
	if req.GetPrice() != nil {
		order.Price = decimal.NewFromProto(*req.Price)
	}
	if req.Qty != nil {
		order.Qty = decimal.NewFromProto(*req.Qty)
	}
	order.HoldingAccountId = req.HoldingAccountId
	order.ServiceAccountId = req.ServiceAccountId
	order.QuoteBalanceAccountId = req.QuoteBalanceAccountId
	order.BaseBalanceAccountId = req.BaseBalanceAccountId
	order.TimeInForce, _ = utils.ProtoToTimeInForce(req.TimeInForce)
	order.ExpireTime = req.ExpireTime
	order.TimerId = 0
	order.CreateTime = uint64(GetClusterTimeUnixNano())
	log.Info().Msgf("order %s createTime=%d", order.ClOrdId, order.CreateTime)
	// Should DAY order not GTD and move the set Expire time to validate_order
	//if order.TimeInForce == enum.TimeInForce_GOOD_TILL_DATE {
	//	now := time.Now()
	//	startOfDay := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	//	endOfDay := startOfDay.Add(24*time.Hour - time.Second)
	//	order.ExpireTime = uint64(endOfDay.UnixNano())
	//}
	if req.EarmarkAmt != nil {
		order.EarmarkAmount = decimal.NewFromProto(*req.EarmarkAmt)
	}
	if req.EarmarkFeeAmt != nil {
		order.EarmarkFee = decimal.NewFromProto(*req.EarmarkFeeAmt)
	}
	if req.EarmarkTaxAmt != nil {
		order.EarmarkTax = decimal.NewFromProto(*req.EarmarkTaxAmt)
	}
	if req.ContractAccrIntAmt != nil {
		order.ContractAccrIntAmt = decimal.NewFromProto(*req.ContractAccrIntAmt)
	}
	if req.BondNominalAmount != nil {
		order.BondNominalAmount = decimal.NewFromProto(*req.BondNominalAmount)
	}
	order.IsTradeOnCleanPrice = req.IsTradeOnCleanPrice
	order.BondPricePct = req.BondPricePct
	order.SenderCompId = req.SenderCompId
	order.TraceId = req.TraceId
	order.UserId = req.UserId
	order.CancelReason = req.CancelReason
	order.OrigClOrdId = req.OrigClOrdId
	order.TaxId = req.TaxId
	order.GatewayName = req_.GatewayName
	order.OrderValidatorName = req_.OrderValidatorName
	var hx_order_fee_details map[string]*match_api.UDec128
	if req.HxFeesResp != nil {
		hx_order_fee_details = req.HxFeesResp.FeesDetails
		order.FeeStructures, order.TaxStructures = parse_fee_tax_structure(req.HxFeesResp)
		if req.HxFeesResp.FeesAmt != nil {
			order.EarmarkFee = decimal.NewFromProto(*req.HxFeesResp.FeesAmt)
		}
		if req.HxFeesResp.TaxAmt != nil {
			order.EarmarkTax = decimal.NewFromProto(*req.HxFeesResp.TaxAmt)
		}
		order.FeeCurrency = req.HxFeesResp.FeeCurrency
	}

	var msg *KafkaMsg // XXX: remove this later
	msg = kafka_new_msg()
	msg.Source = order.Source // XXX: remove?
	msg.RequestId = order.RequestId
	msg.Ctx = order.Ctx

	ob := orderbooks[order.Symbol]
	if ob == nil {
		if disabled_fatal_unknown_symbol {
			log.Error().Msgf("ME doesn't have symbol %s on cache")
			rejectOrder(order, "Unknown symbol "+order.Symbol, msg, ob)
			send_resp_list(msg, req, "process_order_disabled_fatal_unknown_symbol")
			return
		}
		log.Fatal().Msgf("Unknown symbol %s", order.Symbol)
	}
	ob.mutex.Lock()
	defer ob.mutex.Unlock()
	order.Orderbook = ob
	//log.Info().Msgf("process %s",order.ClOrdId)
	//ob.SpinLock.Lock()
	//defer ob.SpinLock.Unlock()

	err := validate_order(order)
	if err != nil {
		SendCancelByOperatorSessionRespIfAny(session, req, err)
		rejectOrder(order, err.Error(), msg, ob)
	} else {
		switch order.RequestType {
		case match_api.RequestType_REQUEST_TYPE_NEW_ORDER:
			err = process_new_order(order, msg, hx_order_fee_details)
		case match_api.RequestType_REQUEST_TYPE_CANCEL_ORDER:
			err = process_cancel_order(order, msg)
		case match_api.RequestType_REQUEST_TYPE_EDIT_ORDER:
			rejectOrder(order, "Amend order not allowed", msg, ob)
			// err = process_edit_order(order, msg)
		default:
			log.Fatal().Msgf("Unknown request type %d", order.RequestType)
		}
		metrics.OrdersProcessed++
		if err != nil {
			SendCancelByOperatorSessionRespIfAny(session, req, err)
			rejectOrder(order, err.Error(), msg, ob)
		}
	}
	log.Debug().Msgf("--- finish process_order order=%s ---", order.ClOrdId)

	if err == nil{
		SendCancelByOperatorSessionRespIfAny(session, req, nil)
	}
	send_resp_list(msg, req, "process_order")
}

func send_resp_list(msg *KafkaMsg, req *match_api.Order, fromEvent string) {
	msg.resp_list.Responses = msg.responses[:msg.num_responses]
	resp_list := msg.resp_list
	resp_list.SenderCompId = req.SenderCompId
	resp_list.RequestSentTime = req.RequestSentTime
	resp_list.RequestReceivedTime = req.RequestReceivedTime
	resp_list.RequestId = req.RequestId

	if !disable_send_match_out {
		aeron_match_out_send(resp_list, fromEvent)
	}
	if !disable_send_ov_in {
		aeron_client_ov_send(resp_list)
	}

	for _, resp := range resp_list.Responses {
		switch m := resp.Response.(type) {
		case *match_api.Response_ExecReport:
			free_exec_report(resp)
			metrics.ExecReportsSent++
			log.Debug().Msgf("sent exec_report: %+v", m)
		case *match_api.Response_PriceUpdate:
			free_price_update(resp)
			metrics.PriceUpdatesSent++
			log.Debug().Msgf("sent price_update: %+v", m)
		}
	}

	/*isCanceledByOperator := session != nil && order.CancelReason == match_api.CancelReason_CANCEL_BY_EXCHANGE_OPERATOR
	if isCanceledByOperator {
		success := err == nil
		errMsg  := ""
		if !success && err != nil {
			errMsg = err.Error()
		}
		cancelResp := match_api.CancelByOperatorResp{
			Success: success,
			Error: errMsg,
		}
		session_resp := match_api.Response{
			Response: &match_api.Response_CancelByOperatorResp{CancelByOperatorResp: &cancelResp},
		}
		aeron_match_svc_send(session, &session_resp)
	}*/

	free_msg(msg)

	if show_orderbooks {
		for mkt, ob := range orderbooks {
			log.Debug().Msgf("🍈🍈🍈 ob=%+v", ob)
			for _, order := range ob.cl_open_orders {
				log.Debug().Msgf("   Mkt=%s - OrderId=%v - Side=%v - AHA=%v - Price=%v - Qty=%v - OpenQty=%v",
					mkt,
					order.OrderId,
					order.Side,
					order.HoldingAccountId,
					order.Price,
					order.Qty,
					order.OpenQty,
				)
			}
		}
	}
}

func process_clear_orderbook(req *match_api.ClearOrderbookReq, req_ *match_api.Request) {
	log.Info().Msgf("process_clear_orderbook symbol=%s", req.Symbol)
	t := uint64(GetClusterTimeUnixNano())
	if viper.GetBool("allow_clear_orderbook") {
		ob := orderbooks[req.Symbol]
		if ob != nil {
			ob.mutex.Lock()
			defer ob.mutex.Unlock()

			ctx := context.Background()
			var msg *KafkaMsg // XXX: remove this later
			msg = kafka_new_msg()
			ob.bid_levels.Descend(func(level *PriceLevel) bool {
				send_price_level_update(&PriceLevelUpdateParams{
					Ctx:          ctx,
					Symbol:       req.Symbol,
					Side:         enum.Side_BUY,
					Price:        level.price,
					NewQty:       decimal.Zero,
					OldQty:       level.tot_qty,
					Depth:        0,
					Time:         t,
					NumOrders:    level.orders.Len(),
					Ob:           ob,
					Msg:          msg,
					BondPricePct: level.bond_price_pct,
				})
				level.orders = btree.NewG[*Order](2, func(a, b *Order) bool { return a.CreateTime < b.CreateTime })
				level.price = decimal.Zero
				level.bond_price_pct = ""
				level.tot_qty = decimal.Zero
				level.time = 0
				free_price_level(level)
				return true
			})
			ob.ask_levels.Ascend(func(level *PriceLevel) bool {
				level.time = uint64(GetClusterTimeUnixNano())
				send_price_level_update(&PriceLevelUpdateParams{
					Ctx:          ctx,
					Symbol:       req.Symbol,
					Side:         enum.Side_SELL,
					Price:        level.price,
					NewQty:       decimal.Zero,
					OldQty:       level.tot_qty,
					Depth:        0,
					Time:         t,
					NumOrders:    level.orders.Len(),
					Ob:           ob,
					Msg:          msg,
					BondPricePct: level.bond_price_pct,
				})
				level.orders = btree.NewG[*Order](2, func(a, b *Order) bool { return a.CreateTime < b.CreateTime })
				level.price = decimal.Zero
				level.bond_price_pct = ""
				level.tot_qty = decimal.Zero
				level.time = 0
				free_price_level(level)
				return true
			})
			ob.bid_levels = btree.NewG[*PriceLevel](2, func(a, b *PriceLevel) bool { return a.price.LessThan(b.price) })
			ob.ask_levels = btree.NewG[*PriceLevel](2, func(a, b *PriceLevel) bool { return a.price.LessThan(b.price) })
			ob.cl_open_orders = make(map[string]*Order)
			msg.resp_list.Responses = msg.responses[:msg.num_responses]
			resp_list := msg.resp_list
			resp_list.SenderCompId = req_.SenderCompId
			resp_list.RequestSentTime = req_.RequestSentTime
			resp_list.RequestReceivedTime = req_.RequestReceivedTime
			resp_list.RequestId = req_.RequestId

			// tell OV to clear price
			resp_list.Responses = append(resp_list.Responses,
				&match_api.Response{Response: &match_api.Response_ClearPrice{
					ClearPrice: &match_api.ClearPrice{Symbol: req.Symbol},
				}})

			if !disable_send_match_out {
				aeron_match_out_send(resp_list, "process_clear_orderbook")
			}
			if !disable_send_ov_in {
				aeron_client_ov_send(resp_list)
			}
			for _, resp := range resp_list.Responses {
				switch resp.Response.(type) {
				case *match_api.Response_PriceUpdate:
					log.Debug().Msgf("sent response: %+v", resp)
					free_price_update(resp)
					metrics.PriceUpdatesSent++
					log.Debug().Msgf("sent price_update: %+v", *resp.Response.(*match_api.Response_PriceUpdate))
				}
			}
		}
	}
}

func process_config(req *match_api.ConfigReq) {
	log.Debug().Msgf("process_config %v", req)

	switch m := req.Request.(type) {
	case *match_api.ConfigReq_AhaUpdate:
		update_config_aha(m)
	case *match_api.ConfigReq_MarketUpdate:
		update_config_market(m)
	case *match_api.ConfigReq_ContractUpdate:
		update_config_contract(m)
	case *match_api.ConfigReq_MarketRoutineUpdate:
		update_config_market_routine(m)
	case *match_api.ConfigReq_SaaUpdate:
		update_config_saa(m)
	case *match_api.ConfigReq_TickerUpdate:
		update_config_ticker(m)
	case *match_api.ConfigReq_UserUpdate:
		update_config_user(m)
	}
}

func process_get_tickers(req *match_api.GetTickersReq, session cluster.ClientSession) {
	log.Debug().Msgf("process_get_tickers req=%+v", req)
	resp := &match_api.GetTickersResp{
		Tickers: make([]*match_api.Ticker, 0),
	}
	for _, ob := range orderbooks {
		ob.mutex.RLock()
		defer ob.mutex.RUnlock()
		t := &match_api.Ticker{
			Symbol:   ob.Symbol,
			BidPrice: ob.BestBid.ToProtoAlloc(),
			AskPrice: ob.BestAsk.ToProtoAlloc(),
		}
		resp.Tickers = append(resp.Tickers, t)
	}
	resp_ := match_api.Response{}
	resp_.Response = &match_api.Response_GetTickers{GetTickers: resp}
	if session != nil {
		aeron_match_svc_send(session, &resp_)
	}
}

func process_get_orderbook(req *match_api.GetOrderbookReq, session cluster.ClientSession) {
	log.Debug().Msgf("process_get_orderbook req=%+v", req)

	ob := orderbooks[req.Symbol]
	if ob == nil {
		return
	}
	ob.mutex.RLock()
	defer ob.mutex.RUnlock()
	offset := ob.next_price_offset

	resp := &match_api.GetOrderbookResp{
		Symbol:         req.Symbol,
		Bids:           make([]*match_api.PriceLevel, 0),
		Asks:           make([]*match_api.PriceLevel, 0),
		LastTradePrice: ob.LastTradePrice.ToProtoAlloc(),
		LastTradeTime:  ob.LastTradeTime,
		LastTradeQty:   ob.LastTradeQty.ToProtoAlloc(),
	}
	depth := 0
	ob.bid_levels.Descend(func(level *PriceLevel) bool {
		l := &match_api.PriceLevel{
			Price:     level.price.ToProtoAlloc(),
			Qty:       level.tot_qty.ToProtoAlloc(),
			Offset:    offset,
			Time:      level.time,
			NumOrders: uint64(level.orders.Len()),
			Depth:     uint32(depth),
		}
		if level.bond_price_pct != "" {
			l.BondPricePct = level.bond_price_pct
		}
		resp.Bids = append(resp.Bids, l)
		depth++
		return true
	})
	depth = 0
	ob.ask_levels.Ascend(func(level *PriceLevel) bool {
		l := &match_api.PriceLevel{
			Price:     level.price.ToProtoAlloc(),
			Qty:       level.tot_qty.ToProtoAlloc(),
			Offset:    offset,
			Time:      level.time,
			NumOrders: uint64(level.orders.Len()),
			Depth:     uint32(depth),
		}
		if level.bond_price_pct != "" {
			l.BondPricePct = level.bond_price_pct
		}
		depth++
		resp.Asks = append(resp.Asks, l)
		return true
	})
	resp_ := match_api.Response{}
	resp_.Response = &match_api.Response_GetOrderbook{GetOrderbook: resp}
	if viper.GetBool("enable_test_dr") {
		log.Debug().Msgf("Orderbook result %v, open_orders %v", &resp_, ob.cl_open_orders)
	}
	if session != nil {
		aeron_match_svc_send(session, &resp_)
	}
}

func process_get_price_levels(req *match_api.GetPriceLevelsReq, session cluster.ClientSession) {
	log.Debug().Msgf("process_get_price_levels req=%+v", req)

	resp := &match_api.GetPriceLevelsResp{}
	for _, ob := range orderbooks {
		ob.mutex.RLock()
		defer ob.mutex.RUnlock()
		offset := ob.next_price_offset

		ob_resp := &match_api.GetOrderbookResp{
			Symbol:         req.Symbol,
			Bids:           make([]*match_api.PriceLevel, 0),
			Asks:           make([]*match_api.PriceLevel, 0),
			LastTradePrice: ob.LastTradePrice.ToProtoAlloc(),
			LastTradeTime:  ob.LastTradeTime,
			LastTradeQty:   ob.LastTradeQty.ToProtoAlloc(),
		}

		ob.bid_levels.Descend(func(level *PriceLevel) bool {
			l := &match_api.PriceLevel{
				Price:  level.price.ToProtoAlloc(),
				Qty:    level.tot_qty.ToProtoAlloc(),
				Offset: offset,
			}
			if level.bond_price_pct != "" {
				l.BondPricePct = level.bond_price_pct
			}
			ob_resp.Bids = append(ob_resp.Bids, l)
			return true
		})
		ob.ask_levels.Ascend(func(level *PriceLevel) bool {
			l := &match_api.PriceLevel{
				Price:  level.price.ToProtoAlloc(),
				Qty:    level.tot_qty.ToProtoAlloc(),
				Offset: offset,
			}
			if level.bond_price_pct != "" {
				l.BondPricePct = level.bond_price_pct
			}
			ob_resp.Asks = append(ob_resp.Asks, l)
			return true
		})
		resp.Orderbooks = append(resp.Orderbooks, ob_resp)
	}
	resp_ := match_api.Response{}
	resp_.Response = &match_api.Response_GetPriceLevels{GetPriceLevels: resp}
	if session != nil {
		aeron_match_svc_send(session, &resp_)
	}
}

func process_request(req *match_api.Request, session cluster.ClientSession) {
	log.Debug().Msgf("process_request seq=%d req=%+v", req.OutSequence, req.Request)
	switch m := req.Request.(type) {
	case *match_api.Request_Order:
		process_order(m.Order, req, session)
	case *match_api.Request_ClearOrderbook:
		process_clear_orderbook(m.ClearOrderbook, req)
	case *match_api.Request_Disconnect:
		process_disconnect(m.Disconnect, req)
	case *match_api.Request_Config:
		process_config(m.Config)
	case *match_api.Request_GetTickers:
		process_get_tickers(m.GetTickers, session)
	case *match_api.Request_GetOrderbook:
		process_get_orderbook(m.GetOrderbook, session)
	case *match_api.Request_GetPriceLevels:
		process_get_price_levels(m.GetPriceLevels, session)
	}
}

func process_timer_event(timer_id int64, time int64) {
	log.Debug().Msgf("--- start process_timer_event timer_id=%d time=%d ---", timer_id, time)
	if timer_id > 0 {
		order := timer_events[timer_id]
		if order == nil {
			log.Error().Msgf("Unknown timer_id %d", timer_id)
			return
		}
		err := process_expire_order(order)
		if err != nil {
			log.Error().Msgf("process_expire_order err_msg=%s", err.Error())
		}
		delete(timer_events, timer_id)
	} else {
		log.Info().Msgf("host=%s process_timer_event timer_id=%d at %v",
			HOST_NAME, MillisToTimeObject(time))
		process_routine_timer_event(timer_id, uint64(time))
	}
	log.Debug().Msgf("--- finish process_timer_event timer_id=%d time=%d ---", timer_id, time)
}

func match_ob_fifo(ctx context.Context, ob *OrderBook) error {
	log.Info().Msgf("match_ob_fifo sym=%s", ob.Symbol)
	bid_level, _ := ob.bid_levels.Max()
	ask_level, _ := ob.ask_levels.Min()
	if bid_level == nil || ask_level == nil {
		log.Info().Msgf("match_ob_fifo DO NOTHING! host %s match_ob_fifo sym=%s  (bid_level == nil)=%t, (ask_level == nil)=%t",
			HOST_NAME, ob.Symbol, bid_level == nil , ask_level == nil)
		return nil
	}
	if bid_level.price.LessThan(ask_level.price) {
		log.Debug().Msgf("match_ob_fifo DO NOTHING! host %s match_ob_fifo sym=%s  bid_level.price.LessThan(ask_level.price)=true",
			HOST_NAME, ob.Symbol)
		return nil
	}
	orders := btree.NewG[*Order](2, func(a, b *Order) bool { return a.CreateTime < b.CreateTime })
	ob.bid_levels.Ascend(func(level *PriceLevel) bool {
		level.orders.Ascend(func(order *Order) bool {
			log.Info().Msgf("ReplaceOrInsert order into bid_levels btree: %+v", order)
			orders.ReplaceOrInsert(order)
			return true
		})
		return true
	})
	ob.ask_levels.Ascend(func(level *PriceLevel) bool {
		level.orders.Ascend(func(order *Order) bool {
			log.Debug().Msgf("ReplaceOrInsert order into ask_levels btree: %+v", order)
			orders.ReplaceOrInsert(order)
			return true
		})
		return true
	})
	ob.bid_levels.Clear(false)
	ob.ask_levels.Clear(false)
	ct := 0
	orders.Ascend(func(order *Order) bool {
		var msg *KafkaMsg
		msg = kafka_new_msg()
		msg.Source = order.Source
		msg.RequestId = order.RequestId
		msg.Ctx = order.Ctx
		log.Debug().Msgf("host %s match_ob_fifo >> ob_add_order %v", HOST_NAME, order)
		ob_add_order(ctx, order, ob, true, decimal.Zero, msg)
		ct++
		req := &match_api.Order{
			Source:              order.Source,
			RequestId:           order.RequestId,
			SenderCompId:        order.SenderCompId,
			RequestSentTime:     order.CreateTime,
			RequestReceivedTime: order.CreateTime,
		}
		send_resp_list(msg, req, "match_ob_fifo")
		return true
	})
	log.Info().Msgf("PROCESSED match_ob_fifo orders count %d", ct)
	return nil
}

func get_eq_price(ob *OrderBook) decimal.Decimal {
	fmt.Printf("get_eq_price sym=%s\n", ob.Symbol)
	return decimal.Zero // XXX: update decimal and test later
	/*combined_prices := btree.NewG[*CombinedPriceLevel](2, func(a, b *CombinedPriceLevel) bool { return a.Price.Cmp(b.Price) < 0 })
	ob.bid_levels.Ascend(func(level *PriceLevel) bool {
		comb_level, _ := combined_prices.Get(&CombinedPriceLevel{Price: level.price})
		if comb_level == nil {
			comb_level = &CombinedPriceLevel{Price: level.price, CumBidQty: decimal.Zero, CumAskQty: decimal.Zero}
			combined_prices.ReplaceOrInsert(comb_level)
		}
		comb_level.BidQty = level.tot_qty
		return true
	})
	ob.ask_levels.Ascend(func(level *PriceLevel) bool {
		comb_level, _ := combined_prices.Get(&CombinedPriceLevel{Price: level.price})
		if comb_level == nil {
			comb_level = &CombinedPriceLevel{Price: level.price, CumBidQty: decimal.Zero, CumAskQty: decimal.Zero}
			combined_prices.ReplaceOrInsert(comb_level)
		}
		comb_level.AskQty = level.tot_qty
		return true
	})
	cum_bid_qty := decimal.Zero
	combined_prices.Descend(func(comb_level *CombinedPriceLevel) bool {
		cum_bid_qty = cum_bid_qty.Add(comb_level.BidQty)
		comb_level.CumBidQty = cum_bid_qty
		return true
	})
	cum_ask_qty := decimal.Zero
	combined_prices.Ascend(func(comb_level *CombinedPriceLevel) bool {
		cum_ask_qty = cum_ask_qty.Add(comb_level.AskQty)
		comb_level.CumAskQty = cum_ask_qty
		return true
	})
	max_trade_qty := decimal.Zero
	var min_imb_qty decimal.Decimal
	eq_price := decimal.Zero
	combined_prices.Ascend(func(comb_level *CombinedPriceLevel) bool {
		trade_qty := decimal.Min(comb_level.CumBidQty, comb_level.CumAskQty)
		imb_qty := comb_level.CumBidQty.Sub(comb_level.CumAskQty).Abs()
		fmt.Printf("price=%v cum_bid_qty=%v cum_ask_qty=%v trade_qty=%v imb_qty=%v\n", comb_level.Price, comb_level.CumBidQty, comb_level.CumAskQty, trade_qty, imb_qty)
		if trade_qty.IsZero() {
			return true
		}
		if trade_qty.LessThan(max_trade_qty) {
			return true
		}
		if trade_qty.Equal(max_trade_qty) && imb_qty.GreaterThan(min_imb_qty) {
			return true
		}
		if imb_qty.Equal(min_imb_qty) {
			if comb_level.CumBidQty.GreaterThan(comb_level.CumAskQty) {
				if comb_level.Price.LessThan(eq_price) {
					return true
				}
			} else {
				if comb_level.Price.GreaterThan(eq_price) {
					return true
				}
			}
		}
		eq_price = comb_level.Price
		max_trade_qty = trade_qty
		min_imb_qty = imb_qty
		return true
	})
	fmt.Printf("eq_price=%v\n", eq_price)
	return eq_price*/
}

func match_ob_eq(ctx context.Context, ob *OrderBook) error {
	fmt.Printf("match_ob_eq sym=%s\n", ob.Symbol)
	bid_level, _ := ob.bid_levels.Max()
	ask_level, _ := ob.ask_levels.Min()
	if bid_level == nil || ask_level == nil {
		return nil
	}
	if bid_level.price.LessThan(ask_level.price) {
		return nil
	}
	eq_price := get_eq_price(ob)
	ask_orders := btree.NewG[*Order](2, func(a, b *Order) bool { return a.Price.LessThan(b.Price) || a.CreateTime < b.CreateTime })
	ob.ask_levels.Ascend(func(level *PriceLevel) bool {
		level.orders.Ascend(func(order *Order) bool {
			ask_orders.ReplaceOrInsert(order)
			return true
		})
		return true
	})
	ob.ask_levels.Clear(false)
	ask_orders.Ascend(func(order *Order) bool {
		ob_add_order(ctx, order, ob, true, eq_price, nil)
		return true
	})
	return nil
}

func set_market_routine(ctx context.Context, market_id string, rt *Routine) error {
	log.Info().Msgf("set_market_routine: market_id=%s routine_id=%d", market_id, rt.RoutineId)
	mkt := markets[market_id]
	if mkt == nil {
		return fmt.Errorf("Market not found: %s", market_id)
	}
	prev_rt := mkt.ActiveRoutine
	log.Debug().Msgf("prev rt=%+v", prev_rt)
	log.Debug().Msgf("new rt=%+v", rt)
	mkt.ActiveRoutine = rt
	if prev_rt != nil && rt != nil && !prev_rt.AllowMatch && rt.AllowMatch {
		for _, ob := range orderbooks {
			log.Debug().Msgf("check ticker=%s market=%s\n", ob.Symbol, ob.Market.MarketId)
			if ob.Market.MarketId != market_id {
				continue
			}
			ob.mutex.Lock()
			defer ob.mutex.Unlock()
			if rt.UseEqPrice {
				log.Info().Msg(">> Start Process match_ob_eq")
				err := match_ob_eq(ctx, ob)
				if err != nil {
					return err
				}
			} else {
				log.Info().Msg(">> Start Process match_ob_fifo")
				err := match_ob_fifo(ctx, ob)
				if err != nil {
					return err
				}
			}
		}
	}
	return nil
}

/*func load_markets() error {
	log.Debug().Msg("load_markets")
	db, err := sql.Open("mysql", viper.GetString("db_url"))
	if err != nil {
		return fmt.Errorf("Failed to connect to MariaDB: %s\n", err)
	}
	defer db.Close()

	//ctx:=context.Background()

	rows, err := db.Query("SELECT market_id,timezone FROM markets")
	if err != nil {
		return fmt.Errorf("Failed to query markets: %s\n", err)
	}

	for row := rows.Next(); row != false; row = rows.Next() {
		var market_id string
		var timezone string
		err = rows.Scan(&market_id, &timezone)
		if err != nil {
			return fmt.Errorf("Failed to scan market: %s\n", err)
		}
		tz, err := time.LoadLocation(timezone)
		if err != nil {
			return fmt.Errorf("Failed to load timezone: %s %s\n", timezone, err)
		}
		mkt := Market{
			MarketId: market_id,
			Timezone: tz,
		}
		markets[mkt.MarketId] = &mkt
	}

	rows, err = db.Query("SELECT routine_id,market_id,routine_type,state_name,weekdays,allow_create,allow_cancel,allow_amend,allow_match,use_eq_price,allow_self_trade,allow_tif_gtc,allow_tif_gtd,allow_tif_ioc,allow_ord_type_market,allow_ord_type_limit FROM routines")
	if err != nil {
		return fmt.Errorf("Failed to query routines: %s\n", err)
	}

	for row := rows.Next(); row != false; row = rows.Next() {
		var routine_id string
		var market_id string
		var routine_type string
		var state_name string
		var weekdays sql.NullString
		var allow_create sql.NullBool
		var allow_cancel sql.NullBool
		var allow_amend sql.NullBool
		var allow_match sql.NullBool
		var use_eq_price sql.NullBool
		var allow_self_trade sql.NullBool
		var allow_tif_gtc sql.NullBool
		var allow_tif_gtd sql.NullBool
		var allow_tif_ioc sql.NullBool
		var allow_ord_type_market sql.NullBool
		var allow_ord_type_limit sql.NullBool
		err = rows.Scan(&routine_id, &market_id, &routine_type, &state_name, &weekdays, &allow_create, &allow_cancel, &allow_amend, &allow_match, &use_eq_price, &allow_self_trade, &allow_tif_gtc, &allow_tif_gtd, &allow_tif_ioc, &allow_ord_type_market, &allow_ord_type_limit)
		if err != nil {
			return fmt.Errorf("Failed to scan routine: %s\n", err)
		}
		rt := &Routine{
			RoutineId:           routine_id,
			MarketId:            market_id,
			StateName:           state_name,
			AllowSelfTrade:      allow_self_trade.Valid && allow_self_trade.Bool,
			AllowTimeInForceGTC: allow_tif_gtc.Valid && allow_tif_gtc.Bool,
			AllowTimeInForceGTD: allow_tif_gtd.Valid && allow_tif_gtd.Bool,
			AllowTimeInForceIOC: allow_tif_ioc.Valid && allow_tif_ioc.Bool,
			AllowOrdTypeMarket:  allow_ord_type_market.Valid && allow_ord_type_market.Bool,
			AllowOrdTypeLimit:   allow_ord_type_limit.Valid && allow_ord_type_limit.Bool,
			AllowCreate:         allow_create.Valid && allow_create.Bool,
			AllowCancel:         allow_cancel.Valid && allow_cancel.Bool,
			AllowAmend:          allow_amend.Valid && allow_amend.Bool,
			AllowMatch:          allow_match.Valid && allow_match.Bool,
			UseEqPrice:          use_eq_price.Valid && use_eq_price.Bool,
		}
		//routines[rt.RoutineId] = rt
		mkt := markets[rt.MarketId]
		if mkt == nil {
			return fmt.Errorf("Market not found: %s", rt.MarketId)
		}
		if mkt.ActiveRoutine == nil {
			mkt.ActiveRoutine = rt
		}
	}

	return nil
}*/

func load_tickers() error {
	log.Debug().Msg("load_tickers")
	db, err := sql.Open("mysql", viper.GetString("db_url"))
	if err != nil {
		return fmt.Errorf("Failed to connect to MariaDB: %s\n", err)
	}
	defer db.Close()

	//ctx:=context.Background()

	rows, err := db.Query("SELECT symbol,market_id,price_decimals,price_mult,qty_decimals,qty_mult,qty_min,qty_max,maker_fee_pct,taker_fee_pct FROM tickers")
	if err != nil {
		return fmt.Errorf("Failed to query markets: %s\n", err)
	}

	for row := rows.Next(); row != false; row = rows.Next() {
		var sym string
		var market_id string
		var price_decimals int
		var price_mult decimal.Decimal
		var qty_decimals int
		var qty_mult decimal.Decimal
		var qty_min decimal.Decimal
		var qty_max decimal.Decimal
		var maker_fee_pct decimal.Decimal
		var taker_fee_pct decimal.Decimal
		err = rows.Scan(&sym, &market_id, &price_decimals, &price_mult, &qty_decimals, &qty_mult, &qty_min, &qty_max, &maker_fee_pct, &taker_fee_pct)
		if err != nil {
			return fmt.Errorf("Failed to scan ticker: %s\n", err)
		}
		log.Debug().Msgf("ticker: %s", sym)
		mkt := markets[market_id]
		if mkt == nil {
			return fmt.Errorf("Market not found: %s", market_id)
		}
		ob := OrderBook{
			Symbol:         sym,
			bid_levels:     btree.NewG[*PriceLevel](2, func(a, b *PriceLevel) bool { return a.price.LessThan(b.price) }),
			ask_levels:     btree.NewG[*PriceLevel](2, func(a, b *PriceLevel) bool { return a.price.LessThan(b.price) }),
			cl_open_orders: make(map[string]*Order),
			Market:         mkt,
			PriceDecimals:  price_decimals,
			PriceMult:      price_mult,
			QtyDecimals:    qty_decimals,
			QtyMult:        qty_mult,
			QtyMin:         qty_min,
			QtyMax:         qty_max,
			MakerFeeRate:   maker_fee_pct.Mul(decimal.NewFromInt(100)),
			TakerFeeRate:   taker_fee_pct.Mul(decimal.NewFromInt(100)),
		}
		orderbooks[sym] = &ob
	}
	return nil
}

func load_orderbooks_db() error {
	log.Debug().Msg("load_orderbooks_db")
	db, err := sql.Open("mysql", viper.GetString("db_url"))
	if err != nil {
		return fmt.Errorf("Failed to connect to MariaDB: %s\n", err)
	}
	defer db.Close()

	ctx := context.Background()

	for _, ob := range orderbooks {
		fmt.Printf("load orderbook: %s", ob.Symbol)
		sym := ob.Symbol
		rows, err := db.Query("SELECT order_id,cl_ord_id,symbol,side,ord_type,qty,price,open_qty,fill_qty,create_time FROM orders WHERE ord_status='new' AND symbol=?", sym)
		if err != nil {
			return fmt.Errorf("Failed to query orders: %s\n", err)
		}

		for row := rows.Next(); row != false; row = rows.Next() {
			var order_id string
			var cl_ord_id string
			var symbol string
			var side string
			var ord_type string
			var qty decimal.Decimal
			var price decimal.Decimal
			var open_qty decimal.Decimal
			var fill_qty decimal.Decimal
			var create_time uint64
			err = rows.Scan(&order_id, &cl_ord_id, &symbol, &side, &ord_type, &qty, &price, &open_qty, &fill_qty, &create_time)
			if err != nil {
				return fmt.Errorf("Failed to scan order: %s\n", err)
			}
			side_, err := utils.StrToSide(side)
			if err != nil {
				return err
			}
			ord_type_, err := utils.StrToOrdType(ord_type)
			if err != nil {
				return err
			}
			order := &Order{
				CreateTime:  create_time,
				OrderId:     order_id,
				ClOrdId:     cl_ord_id,
				Symbol:      symbol,
				Side:        side_,
				OrdType:     ord_type_,
				Qty:         qty,
				Price:       price,
				OrdStatus:   enum.OrdStatus_NEW,
				OpenQty:     open_qty,
				CumTradeQty: fill_qty,
			}
			log.Debug().Msgf("load order: %+v", order)
			err = ob_add_order(ctx, order, ob, false, decimal.Zero, nil)
			if err != nil {
				return err
			}
		}
	}
	return nil
}

func load_orderbooks_redis() error {
	log.Debug().Msgf("load_orderbooks_redis: %d tickers", len(orderbooks))
	//conn, err := redis.Dial("tcp", viper.GetString("redis_addr"))
	tlsConfig := &tls.Config{
		InsecureSkipVerify: true,
	}
	conn, err := redis.DialURL(viper.GetString("redis_url"), redis.DialTLSConfig(tlsConfig))
	if err != nil {
		return fmt.Errorf("Failed to connect to Redis: %s\n", err)
	}
	defer conn.Close()

	ctx := context.Background()
	prefix := viper.GetString("redis_prefix")

	for _, ob := range orderbooks {
		log.Debug().Msgf("load orderbook: %s", ob.Symbol)
		sym := ob.Symbol
		hashKey := prefix + fmt.Sprintf("open_orders:%s", sym)

		orderIDs, err := redis.Strings(conn.Do("HKEYS", hashKey))
		if err != nil {
			return fmt.Errorf("Failed to get orderIDs: %s\n", err)
		}

		for _, orderID := range orderIDs {
			orderJSON, err := redis.String(conn.Do("HGET", hashKey, orderID))
			if err != nil {
				log.Printf("Failed to get order for ID %d: %s\n", orderID, err)
				continue
			}

			rep := &match_api.ExecReport{}
			err = json.Unmarshal([]byte(orderJSON), rep)
			if err != nil {
				return fmt.Errorf("Failed to unmarshal order: %s\n", err)
			}

			order := new_order()
			order.CreateTime = rep.CreateTime
			order.OrderId = rep.OrderId
			order.ClOrdId = rep.ClOrdId
			order.Symbol = rep.Symbol
			order.Side, _ = utils.ProtoToSide(rep.Side)
			order.OrdType, _ = utils.ProtoToOrdType(rep.OrdType)
			order.Qty = decimal.NewFromProto(*rep.Qty)
			order.Price = decimal.NewFromProto(*rep.Price)
			order.OrdStatus, _ = utils.ProtoToOrdStatus(rep.OrdStatus)
			order.OpenQty = decimal.NewFromProto(*rep.OpenQty)
			order.CumTradeQty = decimal.NewFromProto(*rep.FillQty)
			order.HoldingAccountId = rep.HoldingAccountId
			order.ServiceAccountId = rep.ServiceAccountId
			//order.LeavesQty,_=decimal.NewFromString(rep.LeavesQty)
			//order.CumQty,_=decimal.NewFromString(rep.CumQty)
			//order.AvgPx,_=decimal.NewFromString(rep.AvgPx)

			/*err = validate_order_local(order)
			if err != nil {
				return fmt.Errorf("Failed to validate order %s: %s\n", order.OrderId, err)
			}*/

			log.Debug().Msgf("load order: %+v", order)
			err = ob_add_order(ctx, order, ob, false, decimal.Zero, nil)
			if err != nil {
				return err
			}
		}
	}
	return nil
}

func match_start() {
	log.Debug().Msg("match_start")
	match_delay = viper.GetInt("match_delay")
	//orderbooks = make(map[string]*OrderBook)
	/*err := load_markets()
	if err != nil {
		panic(err)
	}
	err = load_tickers()
	if err != nil {
		panic(err)
	}
	/*err=load_orderbooks_db()
	if err!=nil {
		panic(err)
	}*/
	if !viper.GetBool("disable_load_orderbooks") {
		err := load_orderbooks_redis()
		if err != nil {
			panic(err)
		}
	}

	// @Deprecated no more redis
	//if !viper.GetBool("disable_load_balances") {
	//	err := load_balances_redis()
	//	if err != nil {
	//		log.Fatal().Err(err).Msg("Failed to load balances")
	//	}
	//}
}

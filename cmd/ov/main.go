package main

import (
	"context"
	//"fmt"
	//	"log"
	"flag"

	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
	"github.com/rs/zerolog/pkgerrors"
	"github.com/spf13/viper"

	"net/http"
	_ "net/http/pprof"
	"os"
	"os/signal"
	"time"

	//"runtime"
	"syscall"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/exporters/otlp/otlptrace"
	"go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracegrpc"
	"go.opentelemetry.io/otel/sdk/resource"
	sdktrace "go.opentelemetry.io/otel/sdk/trace"
	semconv "go.opentelemetry.io/otel/semconv/v1.17.0"
	"go.opentelemetry.io/otel/trace"

	"errors"
	"fmt"
	"me_two/cmd/ov/hx_fees"

	"github.com/getsentry/sentry-go"
	//"pgregory.net/rand"
	"sync"
	"me_two/pkg/utils"
)

var tracer trace.Tracer
var sentry_enabled bool
var service_name string
var log_balance bool
var sig_term bool
var host = ""
var enable_match_out_backup bool
var enable_sor_out_backup bool
var hx_fee_enabled bool
var clob_fees_enabled bool
var ps_fees_enabled bool
var disable_take_snapshot bool
var disable_me_send_ov_in bool

var DB_TYPE = MariaDB

var price_streaming_enabled = false

var market_mode_ps = false
var market_mode_clob = false
var exitChan = make(chan bool, 1)

var max_retry_cluster_pub_not_connected_sec = 10

const (
	MariaDB  = "mariadb"
	Postgres = "postgres"
)

func main() {
	configPath := flag.String("config", "config.yaml", "path to the configuration file")
	flag.Parse()
	viper.SetConfigFile(*configPath)

	err := viper.ReadInConfig()
	if err != nil {
		log.Fatal().Msgf("Error reading config file, %s", err)
	}
	viper.SetDefault("sor_send_own_enable", true)
	zerolog.ErrorStackMarshaler = pkgerrors.MarshalStack
	log_level := viper.GetString("log_level")
	if log_level == "debug" {
		log.Logger = log.Output(zerolog.ConsoleWriter{Out: os.Stderr}).Level(zerolog.DebugLevel)
	} else {
		log.Logger = log.Output(zerolog.ConsoleWriter{Out: os.Stderr}).Level(zerolog.InfoLevel)
	}

	service_name = viper.GetString("service_name")
	if service_name == "" {
		log.Fatal().Msg("service_name config is missing")
	}

	host, err = os.Hostname()
	if err != nil {
		log.Fatal().Msg("unable to get host name")
	}

	log.Info().Msg("Starting order validator...")

	log_balance = viper.GetBool("log_balance_enabled")

	// Initialize sentry
	sentry_enabled = viper.GetBool("sentry_enabled")
	if sentry_enabled {
		utils.InitializeSentry()		
	}

	//18-Nov-2024. change to use sequence instead!
	//ov_rand = rand.New()
	initOrderNumberFormat()
	initExecIdFormat()
	initBalanceIdFormat()

	initPsOrderNumberFormat()
	if viper.GetBool("enable_tracing") {
		otlp_addr := viper.GetString("otlp_addr")
		if otlp_addr == "" {
			log.Fatal().Msgf("otlp_addr is not set")
		}
		ctx := context.Background()
		exp, err := otlptrace.New(
			ctx,
			otlptracegrpc.NewClient(
				otlptracegrpc.WithInsecure(), // assuming the collector is not using TLS
				otlptracegrpc.WithEndpoint(otlp_addr),
			),
		)
		if err != nil {
			log.Fatal().Msgf("failed to create new OTLP trace exporter: %v", err)
		}
		trace_sample_ratio := viper.GetFloat64("trace_sample_ratio")
		if trace_sample_ratio == 0 {
			trace_sample_ratio = 0.1
		}
		tp := sdktrace.NewTracerProvider(
			sdktrace.WithBatcher(exp),
			sdktrace.WithResource(resource.NewWithAttributes(
				semconv.SchemaURL,
				semconv.ServiceNameKey.String(service_name),
			)),
			sdktrace.WithSampler(sdktrace.TraceIDRatioBased(trace_sample_ratio)),
		)
		otel.SetTracerProvider(tp)
		tracer = otel.Tracer("me_two-tracer")
	}

	disable_balance_check = viper.GetBool("disable_balance_check")
	disable_check_duplicate = viper.GetBool("disable_check_duplicate")
	disable_send_match_out = viper.GetBool("disable_send_match_out")
	disable_send_match_responses = viper.GetBool("disable_send_match_responses")
	disable_delete_account = viper.GetBool("disable_delete_account")
	enable_match_out_backup = viper.GetBool("enable_match_out_backup")
	enable_sor_out_backup = viper.GetBool("enable_sor_out_backup")
	disable_replay_match_out = viper.GetBool("disable_replay_match_out")
	disable_me_send_ov_in = viper.GetBool("disable_me_send_ov_in")
	hx_fee_enabled = viper.GetBool("hx_fees_enabled")
	max_cluster_pub_retry := viper.GetInt("max_retry_cluster_pub_not_connected_sec")
	if max_cluster_pub_retry > 0 {
		max_retry_cluster_pub_not_connected_sec = max_cluster_pub_retry
	}
	// Market model fees feature flag
	// Set clob_fees_enabled default true
	viper.SetDefault("clob_fees_enabled", true)
	clob_fees_enabled = viper.GetBool("clob_fees_enabled")
	viper.SetDefault("ps_fees_enabled", true)
	ps_fees_enabled = viper.GetBool("ps_fees_enabled")
	disable_take_snapshot = viper.GetBool("disable_take_snapshot")
	hx_fees.HxFeeEnabled = hx_fee_enabled
	hx_fees.ClobFeesEnabled = clob_fees_enabled
	hx_fees.PsFeesEnabled = ps_fees_enabled
	hx_fees.LogFee = viper.GetBool("fee_log_enabled")
	enable_print_metrics = viper.GetBool("enable_print_metrics")
	if viper.GetBool("await_aeron_md") {
		aeron_dir := viper.GetString("aeron_dir")
		for {
			_, err := os.Stat(fmt.Sprintf("%s/cnc.dat", aeron_dir))
			if errors.Is(err, os.ErrNotExist) {
				log.Info().Msgf("File %s does not exist, waiting for 1 seconds", aeron_dir)
				time.Sleep(time.Second * 1)
			} else {
				log.Info().Msgf("File %s exists. Continue starting application", aeron_dir)
				break
			}
		}
	}

	if viper.GetBool("use_hx_config") {
		_dbtype := viper.GetString("db_type")
		if _dbtype != "" {
			if _dbtype != MariaDB && _dbtype != Postgres {
				log.Fatal().Msgf("supporting DB_TYPE only for %s or %s", MariaDB, Postgres)
			}
			DB_TYPE = _dbtype
		}
		hx_load_config()
	}

	if enable_print_metrics {
		metrics_start()
	}

	graceFullClosePubFn, _ := aeron_pub_ov_out_start()

	use_cluster = viper.GetBool("use_cluster")

	market_mode := viper.GetStringSlice("market_mode")
	for _, mode := range market_mode {
		if mode == "PS" {
			market_mode_ps = true
			continue
		}
		if mode == "CLOB" {
			market_mode_clob = true
			continue
		}
	}

	startHttpServer()

	// During cluster replay input log will needed match / sor client
	if use_cluster {
		if market_mode_ps {
			aeron_client_sor_start()
		}
		if market_mode_clob {
			aeron_client_match_start()
		}
		//request_price_levels()
	} else {
		aeron_sub_ov_in_start()
		aeron_pub_match_in_start()
	}

	useBond := false
	if market_mode_clob {
		useBond = viper.GetBool("use_bond")
	}
	if use_cluster {
		config := OVConfig{
			useBond:  useBond,
			exitChan: exitChan,
		}
		aeron_svc_ov_start(config)
	}

	if market_mode_clob {
		// must to wait is OV clustered client is already connected
		go func() {
			if disable_me_send_ov_in {
				log.Info().Msg("waiting AeronSvcOVConnected before to aeron_sub_match_out_start")
				for {
					//<-sig_cluster_connected
					awaitSignalOVClusterReplayInputLog("market_mode_clob")				
					log.Info().Msgf("AeronSvcOVConnected=%t Going to aeron_sub_match_out_start", AeronSvcOVConnected)
					aeron_sub_match_out_start()
					if enable_match_out_backup {
						awaitMatchArchiveMerged("match_out_backup")
						aeron_sub_match_out_backup_start()
					}
					break
				}
			}else {
				awaitSignalOVClusterReplayInputLog("market_mode_clob")
			}
		}()
	}

	if market_mode_ps {
		// must to wait is OV clustered client is already connected
		price_streaming_enabled = true
		go func() {
			log.Info().Msg("waiting AeronSvcOVConnected before to aeron_sub_sor_out_start")
			for {
				//<-sig_cluster_connected
				log.Info().Msgf("[SIGNAL] market_mode_ps Waiting Replay Input Log To Be DONE!")
				awaitSignalOVClusterReplayInputLog("market_mode_ps")
				if market_mode_clob {
					awaitMatchArchiveMerged("aeron_sub_sor_out_start")
					awaitMatchArchiveBackupMerged("aeron_sub_sor_out_start")
				}
				log.Info().Msgf("AeronSvcOVConnected=%t Going to aeron_sub_sor_out_start", AeronSvcOVConnected)
				aeron_sub_sor_out_start()
				if enable_sor_out_backup {
					awaitSORArchiveMerged("sor_out_backup")
					aeron_sub_sor_out_backup_start()
				}
				break
			}
		}()
	}


	sigtermChannel := make(chan os.Signal, 1)
	signal.Notify(sigtermChannel, syscall.SIGTERM, syscall.SIGHUP, syscall.SIGINT)

	interval := viper.GetInt("aeron_snapshot_interval")
	if interval <= 0 {
		interval = 3600
	}
	ticker := time.NewTicker(time.Duration(interval) * time.Second)
	defer ticker.Stop()

	// Flush buffered events before the program terminates.
	defer func() {
		if sentry_enabled {
			sentry.Flush(1 * time.Second)
		}
	}()

	go func() {
		for {
			select {
			case <-ticker.C:
				aeron_svc_ov_take_snapshot(false, exitChan)
			case <-sigtermChannel:
				log.Info().Msg("SIGTERM received...")
				sig_term = true
				//this is only an signal to input log not really taking snapshot!
				//it turn out when restart after restore and replay then take snapshot!
				aeron_svc_ov_take_snapshot(true, exitChan)
				graceFullClosePubFn()
				exitChan <- true
				return
			}
		}
	}()

	<-exitChan
	fmt.Println("Stopping order validator...")
}

func ExitApplication(reason string) {
	log.Error().Msgf("Received Signal To Exit Application. Terminate application! %s reason=%s",
		host, reason)
	exitChan <- true
}

func startHttpServer()  {
	log.Info().Msg("Starting HTTP Server...")
	wg := sync.WaitGroup{}
	wg.Add(1)
	go func() {
		failedCount := 0
		firstCall := false
		unhealthyTemplateMsg := `
        [PROBES_FAILED] host=[%s] Probe=[%s] FailedCount=[%d]
        =====================================================
		 - ovClusterAlive=%t 
		 - matchClusterClientAlive=%t 
		 - sorClusterClientAlive=%t
		 - ovPublicationAlive=%t
		 - subMatchArchiveAlive=%t
		 - subMatchArchiveBackupAlive=%t
		 - subSorArchiveAlive=%t
		 - subSorArchiveBackupAlive=%t
		======================================================`
		wg.Done()
		log.Info().Msg("HTTP Server Started!")
		httpAddr := viper.GetString("http_addr")
		log.Info().Msgf("Starting http server on %s", httpAddr)
		templateJson :=`{
                          "status": "%s",
						  "components": {
							"match_cluster_client": {"status": "%s"},
							"sor_cluster_client": {"status": "%s"},
							"ov_publication": {"status": "%s"},
							"archive_sub_match": {"status": "%s"},
							"archive_sub_match_backup": {"status": "%s"},
							"archive_sub_sor": {"status": "%s"},
							"archive_sub_sor_backup": {"status": "%s"}
						  }
					   }`
		http.HandleFunc("/health", func(w http.ResponseWriter, r *http.Request) {
			isAlive := ovClusterAlive()
			var err error
			matchClusterClientAlive:= matchClusterClientAlive()
			sorClusterClientAlive:= sorClusterClientAlive()
			ovPublicationAlive := ovPublicationAlive()
			subMatchArchiveAlive:= subMatchArchiveAlive()
			subMatchArchiveBackupAlive:= subMatchArchiveBackupAlive()
			subSorArchiveAlive:= subSorArchiveAlive()
			subSorArchiveBackupAlive:= subSorArchiveBackupAlive()
			probeCaller := "READINESS"
			if !firstCall {
				probeCaller = "LIVENESS"
			}
			firstCall = false
			if isAlive {
				failedCount = 0
				w.WriteHeader(http.StatusOK)
				_, err = fmt.Fprintf(w,templateJson,
					toState(isAlive),
					toState(matchClusterClientAlive),
					toState(sorClusterClientAlive),
					toState(ovPublicationAlive),
					toState(subMatchArchiveAlive),
					toState(subMatchArchiveBackupAlive),
					toState(subSorArchiveAlive),
					toState(subSorArchiveAlive),
					toState(subSorArchiveBackupAlive),
					)
			}else {
				failedCount++
				log.Warn().Msgf(unhealthyTemplateMsg,
					host,
					probeCaller, failedCount,
					isAlive, matchClusterClientAlive, sorClusterClientAlive, ovPublicationAlive, subMatchArchiveAlive,
					subMatchArchiveBackupAlive, subSorArchiveAlive, subSorArchiveBackupAlive)
				w.WriteHeader(http.StatusInternalServerError)
				_, err = fmt.Fprintf(w, templateJson,
					toState(isAlive),
					toState(matchClusterClientAlive),
					toState(sorClusterClientAlive),
					toState(ovPublicationAlive),
					toState(subMatchArchiveAlive),
					toState(subMatchArchiveBackupAlive),
					toState(subSorArchiveAlive),
					toState(subSorArchiveAlive),
					toState(subSorArchiveBackupAlive),
				)
			}
			if err != nil {
				log.Err(err).Msg("Failed to write response")
			}
		})

		err := http.ListenAndServe(httpAddr, nil)
		if err != nil {
			log.Fatal().Msgf("FAILED TO START HTTP SERVER")
		}
	}()
	wg.Wait()
}
